/*
 * Copyright 2018-2019 Autoware Foundation. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <imm_ukf_pda/imm_ukf_pda.h>

// ImmUkfPda类的构造函数，初始化追踪相关的各种参数
ImmUkfPda::ImmUkfPda()
    : target_id_(0) // 为每个追踪目标分配一个唯一的ukf_id_
      ,
      init_(false) // 初始化标志
      ,
      frame_count_(0) // 帧计数
      ,
      has_subscribed_vectormap_(false) // 是否订阅了矢量地图
      ,
      private_nh_("~") // 初始化私有节点句柄
{
  // 从参数服务器获取配置参数
  private_nh_.param<std::string>("tracking_frame", tracking_frame_, "world");                   // 追踪坐标系
  private_nh_.param<int>("life_time_threshold", life_time_threshold_, 8);                       // 生命周期阈值
  private_nh_.param<double>("gating_threshold", gating_threshold_, 9.22);                       // 门控阈值
  private_nh_.param<double>("gate_probability", gate_probability_, 0.99);                       // 门控概率
  private_nh_.param<double>("detection_probability", detection_probability_, 0.9);              // 检测概率
  private_nh_.param<double>("static_velocity_threshold", static_velocity_threshold_, 0.5);      // 静态速度阈值
  private_nh_.param<int>("static_num_history_threshold", static_num_history_threshold_, 3);     // 静态历史阈值
  private_nh_.param<double>("prevent_explosion_threshold", prevent_explosion_threshold_, 1000); // 防爆阈值
  private_nh_.param<double>("merge_distance_threshold", merge_distance_threshold_, 0.5);        // 合并距离阈值
  private_nh_.param<bool>("use_sukf", use_sukf_, false);                                        // 是否使用sigma点UKF算法

  // 矢量地图辅助追踪的配置
  private_nh_.param<bool>("use_vectormap", use_vectormap_, false);                                     // 是否使用矢量地图
  private_nh_.param<double>("lane_direction_chi_threshold", lane_direction_chi_threshold_, 2.71);      // 车道方向卡方阈值
  private_nh_.param<double>("nearest_lane_distance_threshold", nearest_lane_distance_threshold_, 1.0); // 最近车道距离阈值
  private_nh_.param<std::string>("vectormap_frame", vectormap_frame_, "map");                          // 矢量地图坐标系

  // 用于基准测试的参数配置
  private_nh_.param<bool>("is_benchmark", is_benchmark_, false);         // 是否进行基准测试
  private_nh_.param<std::string>("kitti_data_dir", kitti_data_dir_, ""); // KITTI数据目录
  if (is_benchmark_)
  {
    result_file_path_ = kitti_data_dir_ + "benchmark_results.txt"; // 基准测试结果文件路径
    std::remove(result_file_path_.c_str());                        // 开始新的测试前删除旧的结果文件
  }
}

void ImmUkfPda::run()
{
  pub_object_array_ = node_handle_.advertise<auto_msgs::DetectedObjectArray>("/detection/objects", 1);
  sub_detected_array_ = node_handle_.subscribe("/detection/fusion_tools/objects", 1, &ImmUkfPda::callback, this);

  if (use_vectormap_)
  {
    vmap_.subscribe(private_nh_, vector_map::Category::POINT | vector_map::Category::NODE | vector_map::Category::LANE, 1);
  }
}

void ImmUkfPda::callback(const auto_msgs::DetectedObjectArray &input)
{
  input_header_ = input.header;

  if (use_vectormap_)
  {
    checkVectormapSubscription();
  }

  bool success = updateNecessaryTransform();
  if (!success)
  {
    ROS_INFO("Could not find coordiante transformation");
    return;
  }

  auto_msgs::DetectedObjectArray transformed_input;
  auto_msgs::DetectedObjectArray detected_objects_output;
  transformPoseToGlobal(input, transformed_input);
  tracker(transformed_input, detected_objects_output);
  transformPoseToLocal(detected_objects_output);

  pub_object_array_.publish(detected_objects_output);

  if (is_benchmark_)
  {
    dumpResultText(detected_objects_output);
  }
}

void ImmUkfPda::checkVectormapSubscription()
{
  if (use_vectormap_ && !has_subscribed_vectormap_)
  {
    lanes_ = vmap_.findByFilter([](const vector_map_msgs::Lane &lane)
                                { return true; });
    if (lanes_.empty())
    {
      ROS_INFO("Has not subscribed vectormap");
    }
    else
    {
      has_subscribed_vectormap_ = true;
    }
  }
}

bool ImmUkfPda::updateNecessaryTransform()
{
  bool success = true;
  try
  {
    tf_listener_.waitForTransform(input_header_.frame_id, tracking_frame_, ros::Time(0), ros::Duration(1.0));
    tf_listener_.lookupTransform(tracking_frame_, input_header_.frame_id, ros::Time(0), local2global_);
  }
  catch (tf::TransformException ex)
  {
    ROS_ERROR("%s", ex.what());
    success = false;
  }
  if (use_vectormap_ && has_subscribed_vectormap_)
  {
    try
    {
      tf_listener_.waitForTransform(vectormap_frame_, tracking_frame_, ros::Time(0), ros::Duration(1.0));
      tf_listener_.lookupTransform(vectormap_frame_, tracking_frame_, ros::Time(0), tracking_frame2lane_frame_);
      tf_listener_.lookupTransform(tracking_frame_, vectormap_frame_, ros::Time(0), lane_frame2tracking_frame_);
    }
    catch (tf::TransformException ex)
    {
      ROS_ERROR("%s", ex.what());
    }
  }
  return success;
}

void ImmUkfPda::transformPoseToGlobal(const auto_msgs::DetectedObjectArray &input,
                                      auto_msgs::DetectedObjectArray &transformed_input)
{
  transformed_input.header = input_header_;
  for (auto const &object : input.objects)
  {
    geometry_msgs::Pose out_pose = getTransformedPose(object.pose, local2global_);

    auto_msgs::DetectedObject dd;
    dd.header = input.header;
    dd = object;
    dd.pose = out_pose;

    transformed_input.objects.push_back(dd);
  }
}

void ImmUkfPda::transformPoseToLocal(auto_msgs::DetectedObjectArray &detected_objects_output)
{
  detected_objects_output.header = input_header_;

  tf::Transform inv_local2global = local2global_.inverse();
  tf::StampedTransform global2local;
  global2local.setData(inv_local2global);
  for (auto &object : detected_objects_output.objects)
  {
    geometry_msgs::Pose out_pose = getTransformedPose(object.pose, global2local);
    object.header = input_header_;
    object.pose = out_pose;
  }
}

geometry_msgs::Pose ImmUkfPda::getTransformedPose(const geometry_msgs::Pose &in_pose,
                                                  const tf::StampedTransform &tf_stamp)
{
  tf::Transform transform;
  geometry_msgs::PoseStamped out_pose;
  transform.setOrigin(tf::Vector3(in_pose.position.x, in_pose.position.y, in_pose.position.z));
  transform.setRotation(
      tf::Quaternion(in_pose.orientation.x, in_pose.orientation.y, in_pose.orientation.z, in_pose.orientation.w));
  geometry_msgs::PoseStamped pose_out;
  tf::poseTFToMsg(tf_stamp * transform, out_pose.pose);
  return out_pose.pose;
}

// 测量验证函数
void ImmUkfPda::measurementValidation(const auto_msgs::DetectedObjectArray &input, UKF &target,
                                      const bool second_init, const Eigen::VectorXd &max_det_z,
                                      const Eigen::MatrixXd &max_det_s,
                                      std::vector<auto_msgs::DetectedObject> &object_vec,
                                      std::vector<bool> &matching_vec)
{
  // 与原始IMM-PDA不同，此处选择最有可能的测量值
  bool exists_smallest_nis_object = false;                  // 标记是否存在有效的NIS最小的对象
  double smallest_nis = std::numeric_limits<double>::max(); // 存储最小的NIS值
  int smallest_nis_ind = 0;                                 // 最小NIS值的索引

  // 遍历所有检测到的对象
  for (size_t i = 0; i < input.objects.size(); i++)
  {
    double x = input.objects[i].pose.position.x;
    double y = input.objects[i].pose.position.y;

    Eigen::VectorXd meas = Eigen::VectorXd(2); // 创建测量向量
    meas << x, y;                              // 填充测量向量

    Eigen::VectorXd diff = meas - max_det_z;                    // 计算预测和测量之间的差
    double nis = diff.transpose() * max_det_s.inverse() * diff; // 计算NIS值

    // 如果NIS值在门限之内
    if (nis < gating_threshold_)
    {
      if (nis < smallest_nis) // 检查是否为最小NIS
      {
        smallest_nis = nis;
        target.object_ = input.objects[i]; // 更新目标对象
        smallest_nis_ind = i;              // 更新索引
        exists_smallest_nis_object = true; // 设置存在有效对象的标志
      }
    }
  }

  // 如果找到了有效的最小NIS对象
  if (exists_smallest_nis_object)
  {
    matching_vec[smallest_nis_ind] = true;           // 更新匹配向量
    if (use_vectormap_ && has_subscribed_vectormap_) // 如果使用矢量地图
    {
      auto_msgs::DetectedObject direction_updated_object;
      bool use_direction_meas =
          updateDirection(smallest_nis, target.object_, direction_updated_object, target); // 更新方向
      if (use_direction_meas)
      {
        object_vec.push_back(direction_updated_object); // 添加更新方向后的对象
      }
      else
      {
        object_vec.push_back(target.object_); // 添加原对象
      }
    }
    else
    {
      object_vec.push_back(target.object_); // 添加对象到输出向量
    }
  }
}

bool ImmUkfPda::updateDirection(const double smallest_nis, const auto_msgs::DetectedObject &in_object,
                                auto_msgs::DetectedObject &out_object, UKF &target)
{
  bool use_lane_direction = false;
  target.is_direction_cv_available_ = false;
  target.is_direction_ctrv_available_ = false;
  bool get_lane_success = storeObjectWithNearestLaneDirection(in_object, out_object);
  if (!get_lane_success)
  {
    return use_lane_direction;
  }
  target.checkLaneDirectionAvailability(out_object, lane_direction_chi_threshold_, use_sukf_);
  if (target.is_direction_cv_available_ || target.is_direction_ctrv_available_)
  {
    use_lane_direction = true;
  }
  return use_lane_direction;
}

bool ImmUkfPda::storeObjectWithNearestLaneDirection(const auto_msgs::DetectedObject &in_object,
                                                    auto_msgs::DetectedObject &out_object)
{
  geometry_msgs::Pose lane_frame_pose = getTransformedPose(in_object.pose, tracking_frame2lane_frame_);
  double min_dist = std::numeric_limits<double>::max();

  double min_yaw = 0;
  for (auto const &lane : lanes_)
  {
    vector_map_msgs::Node node = vmap_.findByKey(vector_map::Key<vector_map_msgs::Node>(lane.bnid));
    vector_map_msgs::Point point = vmap_.findByKey(vector_map::Key<vector_map_msgs::Point>(node.pid));
    double distance = std::sqrt(std::pow(point.bx - lane_frame_pose.position.y, 2) +
                                std::pow(point.ly - lane_frame_pose.position.x, 2));
    if (distance < min_dist)
    {
      min_dist = distance;
      vector_map_msgs::Node front_node = vmap_.findByKey(vector_map::Key<vector_map_msgs::Node>(lane.fnid));
      vector_map_msgs::Point front_point = vmap_.findByKey(vector_map::Key<vector_map_msgs::Point>(front_node.pid));
      min_yaw = std::atan2((front_point.bx - point.bx), (front_point.ly - point.ly));
    }
  }

  bool success = false;
  if (min_dist < nearest_lane_distance_threshold_)
  {
    success = true;
  }
  else
  {
    return success;
  }

  // map yaw in rotation matrix representation
  tf::Quaternion map_quat = tf::createQuaternionFromYaw(min_yaw);
  tf::Matrix3x3 map_matrix(map_quat);

  // vectormap_frame to tracking_frame rotation matrix
  tf::Quaternion rotation_quat = lane_frame2tracking_frame_.getRotation();
  tf::Matrix3x3 rotation_matrix(rotation_quat);

  // rotated yaw in matrix representation
  tf::Matrix3x3 rotated_matrix = rotation_matrix * map_matrix;
  double roll, pitch, yaw;
  rotated_matrix.getRPY(roll, pitch, yaw);

  out_object = in_object;
  out_object.angle = yaw;
  return success;
}

void ImmUkfPda::updateTargetWithAssociatedObject(const std::vector<auto_msgs::DetectedObject> &object_vec,
                                                 UKF &target)
{
  target.lifetime_++;
  if (!target.object_.label.empty() && target.object_.label != "unknown")
  {
    target.label_ = target.object_.label;
  }
  updateTrackingNum(object_vec, target);
  if (target.tracking_num_ == TrackingState::Stable || target.tracking_num_ == TrackingState::Occlusion)
  {
    target.is_stable_ = true;
  }
}

void ImmUkfPda::updateBehaviorState(const UKF &target, const bool use_sukf, auto_msgs::DetectedObject &object)
{
  if (use_sukf)
  {
    object.behavior_state = MotionModel::CTRV;
  }
  else if (target.mode_prob_cv_ > target.mode_prob_ctrv_ && target.mode_prob_cv_ > target.mode_prob_rm_)
  {
    object.behavior_state = MotionModel::CV;
  }
  else if (target.mode_prob_ctrv_ > target.mode_prob_cv_ && target.mode_prob_ctrv_ > target.mode_prob_rm_)
  {
    object.behavior_state = MotionModel::CTRV;
  }
  else
  {
    object.behavior_state = MotionModel::RM;
  }
}

void ImmUkfPda::initTracker(const auto_msgs::DetectedObjectArray &input, double timestamp)
{
  for (size_t i = 0; i < input.objects.size(); i++)
  {
    double px = input.objects[i].pose.position.x;
    double py = input.objects[i].pose.position.y;
    Eigen::VectorXd init_meas = Eigen::VectorXd(2);
    init_meas << px, py;

    UKF ukf;
    ukf.initialize(init_meas, timestamp, target_id_);
    targets_.push_back(ukf);
    target_id_++;
  }
  timestamp_ = timestamp;
  init_ = true;
}

void ImmUkfPda::secondInit(UKF &target, const std::vector<auto_msgs::DetectedObject> &object_vec, double dt)
{
  if (object_vec.size() == 0)
  {
    target.tracking_num_ = TrackingState::Die;
    return;
  }
  // record init measurement for env classification
  target.init_meas_ << target.x_merge_(0), target.x_merge_(1);

  // state update
  double target_x = object_vec[0].pose.position.x;
  double target_y = object_vec[0].pose.position.y;
  double target_diff_x = target_x - target.x_merge_(0);
  double target_diff_y = target_y - target.x_merge_(1);
  double target_yaw = atan2(target_diff_y, target_diff_x);
  double dist = sqrt(target_diff_x * target_diff_x + target_diff_y * target_diff_y);
  double target_v = dist / dt;

  while (target_yaw > M_PI)
    target_yaw -= 2. * M_PI;
  while (target_yaw < -M_PI)
    target_yaw += 2. * M_PI;

  target.x_merge_(0) = target.x_cv_(0) = target.x_ctrv_(0) = target.x_rm_(0) = target_x;
  target.x_merge_(1) = target.x_cv_(1) = target.x_ctrv_(1) = target.x_rm_(1) = target_y;
  target.x_merge_(2) = target.x_cv_(2) = target.x_ctrv_(2) = target.x_rm_(2) = target_v;
  target.x_merge_(3) = target.x_cv_(3) = target.x_ctrv_(3) = target.x_rm_(3) = target_yaw;

  target.tracking_num_++;
  return;
}

void ImmUkfPda::updateTrackingNum(const std::vector<auto_msgs::DetectedObject> &object_vec, UKF &target)
{
  if (object_vec.size() > 0)
  {
    if (target.tracking_num_ < TrackingState::Stable)
    {
      target.tracking_num_++;
    }
    else if (target.tracking_num_ == TrackingState::Stable)
    {
      target.tracking_num_ = TrackingState::Stable;
    }
    else if (target.tracking_num_ >= TrackingState::Stable && target.tracking_num_ < TrackingState::Lost)
    {
      target.tracking_num_ = TrackingState::Stable;
    }
    else if (target.tracking_num_ == TrackingState::Lost)
    {
      target.tracking_num_ = TrackingState::Die;
    }
  }
  else
  {
    if (target.tracking_num_ < TrackingState::Stable)
    {
      target.tracking_num_ = TrackingState::Die;
    }
    else if (target.tracking_num_ >= TrackingState::Stable && target.tracking_num_ < TrackingState::Lost)
    {
      target.tracking_num_++;
    }
    else if (target.tracking_num_ == TrackingState::Lost)
    {
      target.tracking_num_ = TrackingState::Die;
    }
  }

  return;
}

bool ImmUkfPda::probabilisticDataAssociation(const auto_msgs::DetectedObjectArray &input, const double dt,
                                             std::vector<bool> &matching_vec,
                                             std::vector<auto_msgs::DetectedObject> &object_vec, UKF &target)
{
  double det_s = 0;
  Eigen::VectorXd max_det_z;
  Eigen::MatrixXd max_det_s;
  bool success = true;

  if (use_sukf_)
  {
    max_det_z = target.z_pred_ctrv_;
    max_det_s = target.s_ctrv_;
    det_s = max_det_s.determinant();
  }
  else
  {
    // find maxDetS associated with predZ
    target.findMaxZandS(max_det_z, max_det_s);
    det_s = max_det_s.determinant();
  }

  // prevent ukf not to explode
  if (std::isnan(det_s) || det_s > prevent_explosion_threshold_)
  {
    target.tracking_num_ = TrackingState::Die;
    success = false;
    return success;
  }

  bool is_second_init;
  if (target.tracking_num_ == TrackingState::Init)
  {
    is_second_init = true;
  }
  else
  {
    is_second_init = false;
  }

  // measurement gating
  measurementValidation(input, target, is_second_init, max_det_z, max_det_s, object_vec, matching_vec);

  // second detection for a target: update v and yaw
  if (is_second_init)
  {
    secondInit(target, object_vec, dt);
    success = false;
    return success;
  }

  updateTargetWithAssociatedObject(object_vec, target);

  if (target.tracking_num_ == TrackingState::Die)
  {
    success = false;
    return success;
  }
  return success;
}

void ImmUkfPda::makeNewTargets(const double timestamp, const auto_msgs::DetectedObjectArray &input,
                               const std::vector<bool> &matching_vec)
{
  for (size_t i = 0; i < input.objects.size(); i++)
  {
    if (matching_vec[i] == false)
    {
      double px = input.objects[i].pose.position.x;
      double py = input.objects[i].pose.position.y;
      Eigen::VectorXd init_meas = Eigen::VectorXd(2);
      init_meas << px, py;

      UKF ukf;
      ukf.initialize(init_meas, timestamp, target_id_);
      ukf.object_ = input.objects[i];
      targets_.push_back(ukf);
      target_id_++;
    }
  }
}

void ImmUkfPda::staticClassification()
{
  for (size_t i = 0; i < targets_.size(); i++)
  {
    // targets_[i].x_merge_(2) is referred for estimated velocity
    double current_velocity = std::abs(targets_[i].x_merge_(2));
    targets_[i].vel_history_.push_back(current_velocity);
    if (targets_[i].tracking_num_ == TrackingState::Stable && targets_[i].lifetime_ > life_time_threshold_)
    {
      int index = 0;
      double sum_vel = 0;
      double avg_vel = 0;
      for (auto rit = targets_[i].vel_history_.rbegin(); index < static_num_history_threshold_; ++rit)
      {
        index++;
        sum_vel += *rit;
      }
      avg_vel = double(sum_vel / static_num_history_threshold_);

      if (avg_vel < static_velocity_threshold_ && current_velocity < static_velocity_threshold_)
      {
        targets_[i].is_static_ = true;
      }
    }
  }
}

bool ImmUkfPda::arePointsClose(const geometry_msgs::Point &in_point_a,
                               const geometry_msgs::Point &in_point_b,
                               float in_radius)
{
  return (fabs(in_point_a.x - in_point_b.x) <= in_radius) && (fabs(in_point_a.y - in_point_b.y) <= in_radius);
}

bool ImmUkfPda::arePointsEqual(const geometry_msgs::Point &in_point_a,
                               const geometry_msgs::Point &in_point_b)
{
  return arePointsClose(in_point_a, in_point_b, CENTROID_DISTANCE);
}

bool ImmUkfPda::isPointInPool(const std::vector<geometry_msgs::Point> &in_pool,
                              const geometry_msgs::Point &in_point)
{
  for (size_t j = 0; j < in_pool.size(); j++)
  {
    if (arePointsEqual(in_pool[j], in_point))
    {
      return true;
    }
  }
  return false;
}

auto_msgs::DetectedObjectArray
ImmUkfPda::removeRedundantObjects(const auto_msgs::DetectedObjectArray &in_detected_objects,
                                  const std::vector<size_t> in_tracker_indices)
{
  if (in_detected_objects.objects.size() != in_tracker_indices.size())
    return in_detected_objects;

  auto_msgs::DetectedObjectArray resulting_objects;
  resulting_objects.header = in_detected_objects.header;

  std::vector<geometry_msgs::Point> centroids;
  // create unique points
  for (size_t i = 0; i < in_detected_objects.objects.size(); i++)
  {
    if (!isPointInPool(centroids, in_detected_objects.objects[i].pose.position))
    {
      centroids.push_back(in_detected_objects.objects[i].pose.position);
    }
  }
  // assign objects to the points
  std::vector<std::vector<size_t>> matching_objects(centroids.size());
  for (size_t k = 0; k < in_detected_objects.objects.size(); k++)
  {
    const auto &object = in_detected_objects.objects[k];
    for (size_t i = 0; i < centroids.size(); i++)
    {
      if (arePointsClose(object.pose.position, centroids[i], merge_distance_threshold_))
      {
        matching_objects[i].push_back(k); // store index of matched object to this point
      }
    }
  }
  // get oldest object on each point
  for (size_t i = 0; i < matching_objects.size(); i++)
  {
    size_t oldest_object_index = 0;
    int oldest_lifespan = -1;
    std::string best_label;
    for (size_t j = 0; j < matching_objects[i].size(); j++)
    {
      size_t current_index = matching_objects[i][j];
      int current_lifespan = targets_[in_tracker_indices[current_index]].lifetime_;
      if (current_lifespan > oldest_lifespan)
      {
        oldest_lifespan = current_lifespan;
        oldest_object_index = current_index;
      }
      if (!targets_[in_tracker_indices[current_index]].label_.empty() &&
          targets_[in_tracker_indices[current_index]].label_ != "unknown")
      {
        best_label = targets_[in_tracker_indices[current_index]].label_;
      }
    }
    // delete nearby targets except for the oldest target
    for (size_t j = 0; j < matching_objects[i].size(); j++)
    {
      size_t current_index = matching_objects[i][j];
      if (current_index != oldest_object_index)
      {
        targets_[in_tracker_indices[current_index]].tracking_num_ = TrackingState::Die;
      }
    }
    auto_msgs::DetectedObject best_object;
    best_object = in_detected_objects.objects[oldest_object_index];
    if (best_label != "unknown" && !best_label.empty())
    {
      best_object.label = best_label;
    }

    resulting_objects.objects.push_back(best_object);
  }

  return resulting_objects;
}

void ImmUkfPda::makeOutput(const auto_msgs::DetectedObjectArray &input,
                           const std::vector<bool> &matching_vec,
                           auto_msgs::DetectedObjectArray &detected_objects_output)
{
  auto_msgs::DetectedObjectArray tmp_objects;
  tmp_objects.header = input.header;
  std::vector<size_t> used_targets_indices;
  for (size_t i = 0; i < targets_.size(); i++)
  {

    double tx = targets_[i].x_merge_(0);
    double ty = targets_[i].x_merge_(1);

    double tv = targets_[i].x_merge_(2);
    double tyaw = targets_[i].x_merge_(3);
    double tyaw_rate = targets_[i].x_merge_(4);

    while (tyaw > M_PI)
      tyaw -= 2. * M_PI;
    while (tyaw < -M_PI)
      tyaw += 2. * M_PI;

    tf::Quaternion q = tf::createQuaternionFromYaw(tyaw);

    auto_msgs::DetectedObject dd;
    dd = targets_[i].object_;
    dd.id = targets_[i].ukf_id_;
    dd.velocity.linear.x = tv;
    dd.acceleration.linear.y = tyaw_rate;
    dd.velocity_reliable = targets_[i].is_stable_;
    dd.pose_reliable = targets_[i].is_stable_;

    if (!targets_[i].is_static_ && targets_[i].is_stable_)
    {
      // Aligh the longest side of dimentions with the estimated orientation
      if (targets_[i].object_.dimensions.x < targets_[i].object_.dimensions.y)
      {
        dd.dimensions.x = targets_[i].object_.dimensions.y;
        dd.dimensions.y = targets_[i].object_.dimensions.x;
      }

      dd.pose.position.x = tx;
      dd.pose.position.y = ty;

      if (!std::isnan(q[0]))
        dd.pose.orientation.x = q[0];
      if (!std::isnan(q[1]))
        dd.pose.orientation.y = q[1];
      if (!std::isnan(q[2]))
        dd.pose.orientation.z = q[2];
      if (!std::isnan(q[3]))
        dd.pose.orientation.w = q[3];
    }
    updateBehaviorState(targets_[i], use_sukf_, dd);

    if (targets_[i].is_stable_ || (targets_[i].tracking_num_ >= TrackingState::Init &&
                                   targets_[i].tracking_num_ < TrackingState::Stable))
    {
      tmp_objects.objects.push_back(dd);
      used_targets_indices.push_back(i);
    }
  }
  detected_objects_output = removeRedundantObjects(tmp_objects, used_targets_indices);
}

void ImmUkfPda::removeUnnecessaryTarget()
{
  std::vector<UKF> temp_targets;
  for (size_t i = 0; i < targets_.size(); i++)
  {
    if (targets_[i].tracking_num_ != TrackingState::Die)
    {
      temp_targets.push_back(targets_[i]);
    }
  }
  std::vector<UKF>().swap(targets_);
  targets_ = temp_targets;
}

void ImmUkfPda::dumpResultText(auto_msgs::DetectedObjectArray &detected_objects)
{
  std::ofstream outputfile(result_file_path_, std::ofstream::out | std::ofstream::app);
  for (size_t i = 0; i < detected_objects.objects.size(); i++)
  {
    double yaw = tf::getYaw(detected_objects.objects[i].pose.orientation);

    // KITTI tracking benchmark data format:
    // (frame_number,tracked_id, object type, truncation, occlusion, observation angle, x1,y1,x2,y2, h, w, l, cx, cy,
    // cz, yaw)
    // x1, y1, x2, y2 are for 2D bounding box.
    // h, w, l, are for height, width, length respectively
    // cx, cy, cz are for object centroid

    // Tracking benchmark is based on frame_number, tracked_id,
    // bounding box dimentions and object pose(centroid and orientation) from bird-eye view
    outputfile << std::to_string(frame_count_) << " " << std::to_string(detected_objects.objects[i].id) << " "
               << "Unknown"
               << " "
               << "-1"
               << " "
               << "-1"
               << " "
               << "-1"
               << " "
               << "-1 -1 -1 -1"
               << " " << std::to_string(detected_objects.objects[i].dimensions.x) << " "
               << std::to_string(detected_objects.objects[i].dimensions.y) << " "
               << "-1"
               << " " << std::to_string(detected_objects.objects[i].pose.position.x) << " "
               << std::to_string(detected_objects.objects[i].pose.position.y) << " "
               << "-1"
               << " " << std::to_string(yaw) << "\n";
  }
  frame_count_++;
}

void ImmUkfPda::tracker(const auto_msgs::DetectedObjectArray &input,
                        auto_msgs::DetectedObjectArray &detected_objects_output)
{
  double timestamp = input.header.stamp.toSec();
  std::vector<bool> matching_vec(input.objects.size(), false);

  if (!init_)
  {
    initTracker(input, timestamp);
    makeOutput(input, matching_vec, detected_objects_output);
    return;
  }

  double dt = (timestamp - timestamp_);
  timestamp_ = timestamp;

  // start UKF process
  for (size_t i = 0; i < targets_.size(); i++)
  {
    targets_[i].is_stable_ = false;
    targets_[i].is_static_ = false;

    if (targets_[i].tracking_num_ == TrackingState::Die)
    {
      continue;
    }
    // prevent ukf not to explode
    if (targets_[i].p_merge_.determinant() > prevent_explosion_threshold_ ||
        targets_[i].p_merge_(4, 4) > prevent_explosion_threshold_)
    {
      targets_[i].tracking_num_ = TrackingState::Die;
      continue;
    }

    targets_[i].prediction(use_sukf_, has_subscribed_vectormap_, dt);

    std::vector<auto_msgs::DetectedObject> object_vec;
    bool success = probabilisticDataAssociation(input, dt, matching_vec, object_vec, targets_[i]);
    if (!success)
    {
      continue;
    }

    targets_[i].update(use_sukf_, detection_probability_, gate_probability_, gating_threshold_, object_vec);
  }
  // end UKF process

  // making new ukf target for no data association objects
  makeNewTargets(timestamp, input, matching_vec);

  // static dynamic classification
  staticClassification();

  // making output for visualization
  makeOutput(input, matching_vec, detected_objects_output);

  // remove unnecessary ukf object
  removeUnnecessaryTarget();
}
