/*
 * Copyright 2015-2019  Foundation. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <geometry_msgs/PointStamped.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Twist.h>
#include <ros/ros.h>
#include <sensor_msgs/NavSatFix.h>
#include <sensor_msgs/Imu.h>
#include <std_msgs/Bool.h>
#include <tf/transform_broadcaster.h>

#include <iostream>
// 替换为GeographicLib头文件
#include <GeographicLib/UTMUPS.hpp>
#include <GeographicLib/Geocentric.hpp>

static ros::Publisher pose_publisher;

static ros::Publisher stat_publisher;
static std_msgs::Bool gnss_stat_msg;

static geometry_msgs::PoseStamped _prev_pose;
static geometry_msgs::Quaternion _quat;
static geometry_msgs::Quaternion _imu_orientation;

static double yaw;
static bool _origin_set = false;
static bool _use_fixed_origin = false;
static bool _use_utm_origin = false; // 添加使用UTM坐标作为原点的标志
static bool _imu_ready = false;
static double _origin_x = 0.0;
static double _origin_y = 0.0;
static double _origin_lat = 0.0;
static double _origin_lon = 0.0;
static double _origin_alt = 0.0; // 添加原点海拔高度

static double _antanna_2_base_link = 0.0; //GPS天线到车身base_link距离

// true if position history is long enough to compute orientation
static bool _orientation_ready = false;

// 坐标系类型: 0-WGS84, 1-CGC2000, 默认使用WGS84
static int _coord_system;

static void GNSSCallback(const sensor_msgs::NavSatFixConstPtr &msg)
{
  double x, y;
  int zone;
  bool northp;

  try
  {
    // 使用GeographicLib进行坐标转换
    // 将经纬度转换为UTM坐标
    if (_coord_system == 0)
    {
      // WGS84坐标系 (GeographicLib默认使用WGS84)
      GeographicLib::UTMUPS::Forward(msg->latitude, msg->longitude, zone, northp, x, y);
    }
    else
    {
      // CGC2000坐标系
      // 注意：GeographicLib主要支持WGS84，对于CGC2000可能需要额外处理
      // 这里简化处理，实际应用中可能需要更复杂的转换
      GeographicLib::UTMUPS::Forward(msg->latitude, msg->longitude, zone, northp, x, y);
      // 如果需要CGC2000特定处理，可以在这里添加
    }
  }
  catch (const std::exception &e)
  {
    ROS_ERROR_STREAM("GeographicLib exception: " << e.what());
    return;
  }

  // 设置原点
  if (!_origin_set && !_use_fixed_origin)
  {
    _origin_x = x;
    _origin_y = y;
    _origin_alt = msg->altitude; // 保存原点海拔高度
    _origin_set = true;
    ROS_INFO("Auto origin set at: (%.3f, %.3f, %.3f)", _origin_x, _origin_y, _origin_alt);
  }

  // 相对坐标
  double rel_x = x - _origin_x;
  double rel_y = y - _origin_y;
  double rel_z = msg->altitude - _origin_alt; // 计算相对海拔高度

  // ROS_INFO("pose: (%.3f, %.3f, %.3f)", rel_x, rel_y, rel_z);

  // 创建位姿消息
  geometry_msgs::PoseStamped pose;
  pose.header = msg->header;
  pose.header.frame_id = "map";
  
  // 当前位姿是velodyne的位姿，需要转换为base_link的位姿
  // velodyne在base_link前方约3.4m的位置
  if (_imu_ready) {
    // 使用IMU的朝向计算base_link位置
    tf::Quaternion q_imu(
        _imu_orientation.x,
        _imu_orientation.y,
        _imu_orientation.z,
        _imu_orientation.w);
    
    tf::Quaternion q_rot;
    q_rot.setRPY(0, 0, M_PI / 2.0);
    
    tf::Quaternion q_corrected = q_rot * q_imu;
    q_corrected.normalize();
    
    // 提取偏航角
    tf::Matrix3x3 m(q_corrected);
    double roll, pitch, yaw_angle;
    m.getRPY(roll, pitch, yaw_angle);
    
    // 根据偏航角计算base_link位置
    // velodyne在base_link前方3.4m
    double dx = _antanna_2_base_link * cos(yaw_angle);
    double dy = _antanna_2_base_link * sin(yaw_angle);
    
    // base_link位置 = velodyne位置 - 偏移量
    pose.pose.position.x = rel_x - dx;
    pose.pose.position.y = rel_y - dy;
    pose.pose.position.z = rel_z;
  } else {
    // 如果IMU数据不可用，使用简单的位置
    pose.pose.position.x = rel_x;
    pose.pose.position.y = rel_y;
    pose.pose.position.z = rel_z;
    ROS_WARN_ONCE("IMU data not available, cannot calculate correct base_link position");
  }
  
  // set gnss_stat
  if (pose.pose.position.x == 0.0 || pose.pose.position.y == 0.0 || pose.pose.position.z == 0.0 || msg->status.status != 0)
  {
    gnss_stat_msg.data = false;
  }
  else
  {
    gnss_stat_msg.data = true;
  }

  double distance = sqrt(pow(pose.pose.position.y - _prev_pose.pose.position.y, 2) +
                         pow(pose.pose.position.x - _prev_pose.pose.position.x, 2));
  // std::cout << "distance : " << distance << std::endl;

  if (distance > 0.2)
  {
    yaw = atan2(pose.pose.position.y - _prev_pose.pose.position.y, pose.pose.position.x - _prev_pose.pose.position.x);
    _quat = tf::createQuaternionMsgFromYaw(yaw);
    _prev_pose = pose;
    _orientation_ready = true;
  }

  tf::Quaternion q_imu(
      _imu_orientation.x,
      _imu_orientation.y,
      _imu_orientation.z,
      _imu_orientation.w);

  tf::Quaternion q_rot;
  // q_rot.setRPY(0, 0, -M_PI / 2.0);
  q_rot.setRPY(0, 0, M_PI / 2.0);

  tf::Quaternion q_corrected = q_rot * q_imu;

  q_corrected.normalize();

  // if (_orientation_ready)
  if (_imu_ready)
  {
    // pose.pose.orientation = _quat;
    pose.pose.orientation.x = q_corrected.x();
    pose.pose.orientation.y = q_corrected.y();
    pose.pose.orientation.z = q_corrected.z();
    pose.pose.orientation.w = q_corrected.w();
    pose_publisher.publish(pose);
    stat_publisher.publish(gnss_stat_msg);

    static tf::TransformBroadcaster br;
    tf::Transform transform;
    tf::Quaternion q;
    geometry_msgs::Quaternion q_msg = pose.pose.orientation;
    transform.setOrigin(tf::Vector3(pose.pose.position.x, pose.pose.position.y, pose.pose.position.z));
    tf::quaternionMsgToTF(q_msg, q);
    transform.setRotation(q);
    
    // 发布gps到map的变换（现在gps代表base_link的位置）
    br.sendTransform(tf::StampedTransform(transform, msg->header.stamp, "map", "gps"));
  }
}

static void IMUCallback(const sensor_msgs::ImuConstPtr &msg)
{
  _imu_orientation = msg->orientation;
  _imu_ready = true;
}

int main(int argc, char **argv)
{
  ros::init(argc, argv, "fix2tfpose");
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");

  // 获取坐标系类型参数，默认为0(WGS84)
  private_nh.param("coord_system", _coord_system, 0);
  private_nh.param("use_fixed_origin", _use_fixed_origin, false);
  private_nh.param("origin_lat", _origin_lat, 0.0);
  private_nh.param("origin_lon", _origin_lon, 0.0);
  private_nh.param("origin_alt", _origin_alt, 0.0); // 添加原点海拔高度参数

  // 添加UTM坐标原点参数
  private_nh.param("use_utm_origin", _use_utm_origin, false);
  private_nh.param("origin_utm_x", _origin_x, 0.0);
  private_nh.param("origin_utm_y", _origin_y, 0.0);
  private_nh.param("origin_utm_z", _origin_alt, 0.0); // 使用UTM的Z坐标作为高度

  private_nh.param("antanna_2_base_link", _antanna_2_base_link, 0.0);


  // 如果配置为使用UTM坐标作为原点
  if (_use_utm_origin)
  {
    _origin_set = true;
    _use_fixed_origin = false; // UTM原点优先于经纬度原点
    ROS_INFO("Fixed UTM origin set to: (%.3f, %.3f, %.3f)", _origin_x, _origin_y, _origin_alt);
  }
  // 如果配置为使用固定原点（经纬度），则转换为UTM
  else if (_use_fixed_origin)
  {
    try
    {
      int zone;
      bool northp;
      GeographicLib::UTMUPS::Forward(_origin_lat, _origin_lon, zone, northp, _origin_x, _origin_y);
      _origin_set = true;
      ROS_INFO("Fixed origin set to: lat=%.8f, lon=%.8f, alt=%.3f -> UTM(%.3f, %.3f, %.3f)",
               _origin_lat, _origin_lon, _origin_alt, _origin_x, _origin_y, _origin_alt);
    }
    catch (const std::exception &e)
    {
      ROS_ERROR_STREAM("Failed to set fixed origin: " << e.what());
    }
  }

  pose_publisher = nh.advertise<geometry_msgs::PoseStamped>("gnss_pose", 1000);
  stat_publisher = nh.advertise<std_msgs::Bool>("/gnss_stat", 1000);
  ros::Subscriber gnss_pose_subscriber = nh.subscribe("/chcnav_fix_demo/fix", 100, GNSSCallback);
  ros::Subscriber gnss_imu_subscriber = nh.subscribe("/chcnav_fix_demo/imu", 100, IMUCallback);

  ros::spin();
  return 0;
}
