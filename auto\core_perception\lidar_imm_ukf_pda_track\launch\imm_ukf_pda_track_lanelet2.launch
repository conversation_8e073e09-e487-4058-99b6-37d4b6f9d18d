<!-- -->
<launch>
  <arg name="namespace" default="/detection/object_tracker"/>
  <arg name="tracker_input_topic" default="/detection/fusion_tools/objects" />
  <arg name="tracker_output_topic" default="/detection/object_tracker/objects" />

  <arg name="tracking_frame" default="/world" />
  <arg name="gating_threshold" default="9.22" />
  <arg name="gate_probability" default="0.99" />
  <arg name="detection_probability" default="0.9" />
  <arg name="life_time_threshold" default="8" />
  <arg name="static_velocity_threshold" default="0.5" />
  <arg name="static_num_history_threshold" default="3" />
  <arg name="prevent_explosion_threshold" default="1000" />
  <arg name="merge_distance_threshold" default="0.5"/>
  <arg name="use_sukf" default="false" />

  <!-- Vectormap -->
  <arg name="use_map_info" default="false"/>
  <arg name="lane_direction_chi_threshold" default="2.71" />
  <arg name="nearest_lane_distance_threshold" default="1.0" />
  <arg name="map_frame" default="/map" />

  <!-- rosrun lidar_tracker euclidean_cluster _points_node:="" -->
  <node pkg="imm_ukf_pda_track" type="imm_ukf_pda_lanelet2" name="imm_ukf_pda_01_lanelet2" output="screen">
    <param name="gating_threshold" value="$(arg gating_threshold)" />
    <param name="gate_probability" value="$(arg gate_probability)" />
    <param name="detection_probability" value="$(arg detection_probability)" />
    <param name="life_time_threshold" value="$(arg life_time_threshold)" />
    <param name="static_velocity_threshold" value="$(arg static_velocity_threshold)" />
    <param name="static_num_history_threshold" value="$(arg static_num_history_threshold)" />
    <param name="prevent_explosion_threshold" value="$(arg prevent_explosion_threshold)" />
    <param name="lane_direction_chi_threshold" value="$(arg lane_direction_chi_threshold)" />
    <param name="nearest_lane_distance_threshold" value="$(arg nearest_lane_distance_threshold)" />
    <param name="tracking_frame" value="$(arg tracking_frame)" />
    <param name="map_frame" value="$(arg map_frame)" />
    <param name="use_sukf" value="$(arg use_sukf)" />
    <param name="use_map_info" value="$(arg use_map_info)" />
    <param name="merge_distance_threshold" value="$(arg merge_distance_threshold)" />

    <remap from="detection/fusion_tools/objects" to="$(arg tracker_input_topic)" />
    <remap from="detection/objects" to="$(arg tracker_output_topic)" />
  </node>

  <node pkg="detected_objects_visualizer" type="visualize_detected_objects" name="ukf_track_visualization_01"
        output="screen" ns="$(arg namespace)"/>

  <node pkg="topic_tools" type="relay" name="ukf_track_relay_01"
        output="screen" args="$(arg namespace)/objects /detection/objects"/>

</launch>
