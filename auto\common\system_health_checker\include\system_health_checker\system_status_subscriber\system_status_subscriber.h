/*
 * Copyright 2019  Foundation. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 *
 * v1.0 Ma<PERSON><PERSON>
 */

#ifndef health_checker_SYSTEM_STATUS_SUBSCRIBER_SYSTEM_STATUS_SUBSCRIBER_H
#define health_checker_SYSTEM_STATUS_SUBSCRIBER_SYSTEM_STATUS_SUBSCRIBER_H
// headers in 
#include <system_health_checker/constants.h>
#include <system_msgs/SystemStatus.h>

// headers in ROS
#include <ros/ros.h>

// headers in STL
#include <functional>
#include <memory>
#include <mutex>
#include <vector>

namespace health_checker
{
class SystemStatusSubscriber
{
public:
  SystemStatusSubscriber(ros::NodeHandle nh, ros::NodeHandle pnh);
  void enable();
  void
  addCallback(std::function<void(std::shared_ptr<system_msgs::SystemStatus>)> func);

private:
  void
  systemStatusCallback(const system_msgs::SystemStatus::ConstPtr msg);
  ros::Subscriber status_sub_;
  ros::NodeHandle nh_;
  ros::NodeHandle pnh_;
  std::vector<std::function<void(std::shared_ptr<system_msgs::SystemStatus>)>> functions_;
};
}  // namespace health_checker

#endif  // health_checker_SYSTEM_STATUS_SUBSCRIBER_SYSTEM_STATUS_SUBSCRIBER_H
