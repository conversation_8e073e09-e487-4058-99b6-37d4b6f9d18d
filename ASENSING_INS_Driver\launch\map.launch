<launch>

  <!-- 矢量地图 -->
  <node pkg="map_file" type="vector_map_loader" name="vector_map_loader" output="screen">
    <rosparam command="load" file="$(find ins)/config/map.yaml" />
    <param name="load_mode" value="directory" />
    <!-- 矢量地图路径 -->
    <param name="host_name" value="************" />
    <param name="port" value="80" />
    <param name="user" value="" />
    <param name="password" value="" />
  </node>

  <!-- 点云地图路径 -->
  <node pkg="map_file" type="points_map_loader" name="points_map_loader">
    <rosparam command="load" file="$(find ins)/config/map.yaml" />
  </node>

  <!-- points_downsample(voxel_grid_filter) -->
  <arg name="sync" default="false" />
  <arg name="node_name" default="voxel_grid_filter" />
  <arg name="points_topic" default="/points_raw" />
  <arg name="output_log" default="false" />
  <arg name="measurement_range" default="200" />
  <node pkg="points_downsampler" name="$(arg node_name)" type="$(arg node_name)">
    <param name="points_topic" value="$(arg points_topic)" />
    <remap from="/points_raw" to="/sync_drivers/points_raw" if="$(arg sync)" />
    <param name="output_log" value="$(arg output_log)" />
    <param name="measurement_range" value="$(arg measurement_range)" />
  </node>


  <!-- ring_ground_filter -->
  <arg name="point_topic" default="/points_raw" />
  <arg name="remove_floor" default="true" />
  <arg name="sensor_model" default="16" />
  <arg name="sensor_height" default="1.2" />

  <arg name="max_slope" default="10.0" />
  <arg name="vertical_thres" default="0.08" />

  <arg name="no_ground_point_topic" default="/points_no_ground" />
  <arg name="ground_point_topic" default="/points_ground" />

  <!-- rosrun lidar_tracker ground_filter -->
  <node pkg="points_preprocessor" type="ring_ground_filter" name="ring_ground_filter" output="log">
    <param name="point_topic" value="$(arg point_topic)" />
    <param name="remove_floor" value="$(arg remove_floor)" />
    <param name="sensor_model" value="$(arg sensor_model)" />
    <param name="sensor_height" value="$(arg sensor_height)" />
    <param name="max_slope" value="$(arg max_slope)" />
    <param name="vertical_thres" value="$(arg vertical_thres)" />
    <param name="no_ground_point_topic" value="$(arg no_ground_point_topic)" />
    <param name="ground_point_topic" value="$(arg ground_point_topic)" />
  </node>

  <arg name="info_path" default="$(find ins)/config/vehicle_info.yaml" />

  <rosparam command="load" file="$(arg info_path)" />

  <node pkg="rviz" type="rviz" name="rviz" args="-d $(find ins)/config/default.rviz" />

</launch>