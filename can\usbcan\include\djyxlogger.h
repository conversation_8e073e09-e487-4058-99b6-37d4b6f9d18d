#ifndef DJYX_LOGGER_H
#define DJYX_LOGGER_H

#include <iostream>
#include <string>
#include <sstream>
#include <chrono>
#include <ctime>
#include <unordered_map>
#include <vector>
#include <iomanip>
#include <ros/ros.h>
#include <ros/package.h>
#include <fstream>
#include <mutex>
#include <filesystem>

namespace djyx
{

    class Logger
    {
    public:
        Logger(size_t max_error_count = 100, size_t max_days = 1)
            : max_error_count_(max_error_count), max_duration_(std::chrono::hours(24 * max_days)) {}

        void logError(const std::string &error_message)
        {
            std::lock_guard<std::mutex> lock(mutex_);
            // 获取系统时间
            auto now = std::chrono::system_clock::now();
            auto now_time_t = std::chrono::system_clock::to_time_t(now);
            auto it = last_logged_times.find(error_message);
            if (it != last_logged_times.end())
            {
                // 设置时间阈值，1分钟
                auto time_since_last_log = now - it->second;
                if (time_since_last_log < std::chrono::seconds(60))
                {
                    // 如果没有超过一分钟，不记录该次错误
                    return;
                }
            }

            // 更新最后记录时间
            last_logged_times[error_message] = now;

            // 将时间转换为本地时间
            struct tm now_tm;
            localtime_r(&now_time_t, &now_tm);

            // 创建字符串流构建时间消息
            std::stringstream ss;
            ss << std::put_time(&now_tm, "%Y-%m-%d %H:%M:%S") << " - " << error_message;

            ROS_ERROR("%s\n", ss.str().c_str());
            error_messages_.push_back(ss.str());

            // 写入日志文件
            saveErrorLog("error_log.txt");
        }

        void saveErrorLog(const std::string &filename)
        {
            // std::lock_guard<std::mutex> lock(mutex_);
            std::string package_path = ros::package::getPath("ins");
            std::string full_filename = package_path + "/Debugging_Data/" + filename;

            // 检查并清空文件
            if (shouldClearLog(full_filename))
            {
                std::ofstream clear_file(full_filename, std::ofstream::trunc);
                clear_file.close();
            }

            // 追加保存错误消息
            std::ofstream file(full_filename, std::ofstream::app);
            for (const auto &message : error_messages_)
            {
                file << message << std::endl;
            }
            file.close();
            error_messages_.clear();
        }

    private:
        bool shouldClearLog(const std::string &filename)
        {
            std::ifstream file(filename);
            if (!file.is_open())
            {
                return false;
            }

            std::string line;
            size_t count = 0;
            std::chrono::system_clock::time_point first_timestamp;

            while (std::getline(file, line))
            {
                if (count == 0)
                {
                    // 提取第一条日志的时间戳
                    std::istringstream iss(line.substr(0, 19)); // Assuming timestamp is the first 19 characters
                    struct tm tm = {};
                    iss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
                    first_timestamp = std::chrono::system_clock::from_time_t(std::mktime(&tm));
                }
                ++count;
            }

            file.close();

            auto now = std::chrono::system_clock::now();
            auto duration = now - first_timestamp;

            return count > max_error_count_ || duration > max_duration_;
        }

        std::unordered_map<std::string, std::chrono::system_clock::time_point> last_logged_times;
        std::vector<std::string> error_messages_;
        std::mutex mutex_;
        size_t max_error_count_;
        std::chrono::hours max_duration_; // 使用小时表示最大时间间隔
    };

} // namespace djyx

#endif // DJYX_LOGGER_H