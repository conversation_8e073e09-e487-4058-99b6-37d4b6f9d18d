#include <cmath>
#include <cstdint>
#include <algorithm>
#include <iostream>

// PID控制器
class PIDController
{
public:
  /**
   * @brief 构造函数
   * 
   * @param kp 比例系数
   * @param ki 积分系数
   * @param kd 微分系数
   */
  PIDController(double kp, double ki, double kd)
      : kp(kp), ki(ki), kd(kd), kf(0.0), kv(0.0), deadZone(0.0), integral(0.0), prevError(0.0), prevOutput(0.0), prevCurrent(0.0) {}

  /**
   * @brief 带前馈和速度反馈的构造函数
   * 
   * @param kp 比例系数
   * @param ki 积分系数
   * @param kd 微分系数
   * @param kf 前馈系数
   * @param kv 速度反馈系数
   */
  PIDController(double kp, double ki, double kd, double kf, double kv = 0.0)
      : kp(kp), ki(ki), kd(kd), kf(kf), kv(kv), deadZone(0.0), integral(0.0), prevError(0.0), prevOutput(0.0), prevCurrent(0.0) {}

  /**
   * @brief 设置死区
   * 
   * @param zone 死区大小
   */
  void setDeadZone(double zone)
  {
    deadZone = std::abs(zone);
  }

  /**
   * @brief 传统PID
   *
   * @param target 目标值
   * @param current 当前值
   * @param dt 时间步长
   * @return double
   */
  double calculate(double target, double current, double dt)
  {
    // 计算误差
    double error = target - current;

    // 积分：累积误差以处理稳态误差
    integral += error * dt;

    // 微分：计算误差的变化率，以减少系统震荡
    double derivative = (error - prevError) / dt;

    // PID控制器：当前值与误差、误差积分、误差导数成比例
    double output = kp * error + ki * integral + kd * derivative;

    // 更新前一时刻的误差，用于微分计算
    prevError = error;

    // std::cout << "KP:" << kp * error << std::endl;
    // std::cout << "KI:" << ki * integral << std::endl;
    // std::cout << "KD:" << kd * derivative << std::endl;

    return output;
  }

  /**
   * @brief 抗积分饱和PID
   *
   * @param target 目标值
   * @param current 当前值
   * @param dt 时间步长
   * @param windupLimit 限制积分值
   * @return double
   */
  double calculate(double target, double current, double dt, double windupLimit)
  {
    // 计算误差
    double error = target - current;

    // 积分：累积误差以处理稳态误差
    integral += error * dt;

    integral = std::clamp(integral, -windupLimit, windupLimit);

    // 微分：计算误差的变化率，以减少系统震荡
    double derivative = (error - prevError) / dt;

    // PID控制器：当前值与误差、误差积分、误差导数成比例
    double output = kp * error + ki * integral + kd * derivative;

    // 更新前一时刻的误差，用于微分计算
    prevError = error;

    // std::cout << "error:" << error << std::endl;
    // std::cout << "integral:" << integral << std::endl;
    // std::cout << "KP:" << kp * error << std::endl;
    // std::cout << "KI:" << ki * integral << std::endl;
    // std::cout << "KD:" << kd * derivative << std::endl;

    return output;
  }

  /**
   * @brief 增量式PID控制
   * 输出的是控制量的增量，而不是控制量的绝对值
   * 
   * @param target 目标值
   * @param current 当前值
   * @param dt 时间步长
   * @return double 控制增量
   */
  double calculateIncremental(double target, double current, double dt)
  {
    // 计算误差
    double error = target - current;
    
    // 计算增量
    double deltaP = kp * (error - prevError);
    double deltaI = ki * error * dt;
    double deltaD = kd * (error - 2 * prevError + prevPrevError) / dt;
    
    // 增量式PID输出
    double deltaOutput = deltaP + deltaI + deltaD;
    
    // 更新误差历史
    prevPrevError = prevError;
    prevError = error;
    
    // 返回控制增量
    return deltaOutput;
  }

  /**
   * @brief 带死区的PID控制
   * 当误差小于死区时不进行控制
   * 
   * @param target 目标值
   * @param current 当前值
   * @param dt 时间步长
   * @return double
   */
  double calculateWithDeadZone(double target, double current, double dt)
  {
    // 计算误差
    double error = target - current;
    
    // 应用死区
    if (std::abs(error) < deadZone)
    {
      return 0.0;
    }
    
    // 积分：累积误差以处理稳态误差
    integral += error * dt;
    
    // 微分：计算误差的变化率，以减少系统震荡
    double derivative = (error - prevError) / dt;
    
    // PID控制器：当前值与误差、误差积分、误差导数成比例
    double output = kp * error + ki * integral + kd * derivative;
    
    // 更新前一时刻的误差，用于微分计算
    prevError = error;
    
    return output;
  }

  /**
   * @brief 带前馈的PID控制
   * 前馈控制可以提前响应目标值的变化
   * 
   * @param target 目标值
   * @param current 当前值
   * @param dt 时间步长
   * @return double
   */
  double calculateWithFeedforward(double target, double current, double dt)
  {
    // 计算误差
    double error = target - current;
    
    // 积分：累积误差以处理稳态误差
    integral += error * dt;
    
    // 微分：计算误差的变化率，以减少系统震荡
    double derivative = (error - prevError) / dt;
    
    // 前馈项：直接与目标值成比例
    double feedforward = kf * target;
    
    // PID控制器加前馈项
    double output = kp * error + ki * integral + kd * derivative + feedforward;
    
    // 更新前一时刻的误差，用于微分计算
    prevError = error;
    
    return output;
  }

  /**
   * @brief 带速度反馈的PID控制
   * 速度反馈可以抑制系统的振荡
   * 
   * @param target 目标值
   * @param current 当前值
   * @param dt 时间步长
   * @return double
   */
  double calculateWithVelocityFeedback(double target, double current, double dt)
  {
    // 计算误差
    double error = target - current;
    
    // 积分：累积误差以处理稳态误差
    integral += error * dt;
    
    // 微分：计算误差的变化率，以减少系统震荡
    double derivative = (error - prevError) / dt;
    
    // 速度反馈：当前值的变化率
    double velocity = (current - prevCurrent) / dt;
    
    // PID控制器加速度反馈项
    double output = kp * error + ki * integral + kd * derivative - kv * velocity;
    
    // 更新前一时刻的误差和当前值，用于下次计算
    prevError = error;
    prevCurrent = current;
    
    return output;
  }

  /**
   * @brief 综合PID控制器
   * 包含前馈、速度反馈和死区
   * 
   * @param target 目标值
   * @param current 当前值
   * @param dt 时间步长
   * @param windupLimit 积分饱和限制
   * @return double
   */
  double calculateComprehensive(double target, double current, double dt, double windupLimit)
  {
    // 计算误差
    double error = target - current;
    
    // 应用死区
    if (std::abs(error) < deadZone)
    {
      return prevOutput;
    }
    
    // 积分：累积误差以处理稳态误差，并应用抗饱和
    integral += error * dt;
    integral = std::clamp(integral, -windupLimit, windupLimit);
    
    // 微分：计算误差的变化率，以减少系统震荡
    double derivative = (error - prevError) / dt;
    
    // 速度反馈：当前值的变化率
    double velocity = (current - prevCurrent) / dt;
    
    // 前馈项：直接与目标值成比例
    double feedforward = kf * target;
    
    // 综合PID控制器
    double output = kp * error + ki * integral + kd * derivative + feedforward - kv * velocity;
    
    // 更新状态变量
    prevError = error;
    prevCurrent = current;
    prevOutput = output;
    
    return output;
  }

  /**
   * @brief 重置PID控制器状态
   */
  void resetPIDController()
  {
    prevError = 0.0;
    prevPrevError = 0.0;
    integral = 0.0;
    prevOutput = 0.0;
    prevCurrent = 0.0;
  }

  /**
   * @brief 设置PID参数
   * 
   * @param p 比例系数
   * @param i 积分系数
   * @param d 微分系数
   */
  void setPIDParameters(double p, double i, double d)
  {
    kp = p;
    ki = i;
    kd = d;
  }

  /**
   * @brief 设置前馈和速度反馈参数
   * 
   * @param f 前馈系数
   * @param v 速度反馈系数
   */
  void setFeedforwardVelocityParameters(double f, double v)
  {
    kf = f;
    kv = v;
  }

private:
  // PID参数
  double kp, ki, kd;
  // 前馈和速度反馈参数
  double kf, kv;
  // 死区大小
  double deadZone;
  // 状态变量
  double integral, prevError, prevPrevError;
  double prevOutput, prevCurrent;
};
