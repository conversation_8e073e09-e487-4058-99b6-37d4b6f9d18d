^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package object_map
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

1.11.0 (2019-03-21)
-------------------
* [Feature] costmap generator (`#1774 <https://github.com/CPFL//issues/1774>`_)
  * * Initial commit for visualization package
  * Removal of all visualization messages from perception nodes
  * Visualization dependency removal
  * Launch file modification
  * * Fixes to visualization
  * Error on Clustering CPU
  * Reduce verbosity on markers
  * Publish acceleration and velocity from ukf tracker
  * Remove hardcoded path
  * Updated README
  * updated prototype
  * Prototype update for header and usage
  * Removed unknown label from being reported
  * Updated publishing orientation to match develop
  * * Published all the trackers
  * Added valid field for visualization and future compatibility with ADAS ROI filtering
  * Add simple functions
  * * Reversed back UKF node to develop
  * Formatted speed
  * Refactor codes
  * Make tracking visualization work
  * Relay class info in tracker node
  * Remove dependency to jskbbox and rosmarker in ukf tracker
  * apply rosclang to ukf tracker
  * Refactor codes
  * Revert "apply rosclang to ukf tracker"
  * Revert "Remove dependency to jskbbox and rosmarker in ukf tracker"
  * Revert "Relay class info in tracker node"
  * delete dependency to jsk and remove pointcloud_frame
  * get direction nis
  * set velocity_reliable true in tracker node
  * Add divided function
  * add function
  * Sanity checks
  * Relay all the data from input DetectedObject
  * Divided function work both for immukf and sukf
  * Add comment
  * Refactor codes
  * Pass immukf test
  * make direction assisted tracking work
  * Visualization fixes
  * Refactor codes
  * Tracker Merging step added
  * Added launch file support for merging phase
  * lane assisted with sukf
  * * change only static objects
  * keep label of the oldest tracker
  * Static Object discrimination
  * Non rotating bouding box
  * no disappear if detector works
  * Modify removeRedundant a bit
  * initial commit for costmap generator
  * add vague stucture
  * add brief structure fot points2costmap
  * refactor codes
  * add sensor points costmap
  * add waypoint costmap
  * bug fix for wayarea2costmap
  * add simple structure for objects2costmap
  * add vague structure for waypoints2costmap
  * Replacement of JSK visualization for RViz Native Markers
  * add occupancy grid visualization
  * add objects costmap
  * add minimum height threshold for pointcloud
  * debug computing.yaml from objects_topic to objects_input
  * Add blurred costmap
  * Add comment on computing.yml
  * visualizing bug fix
  * Make sure object's cost is 100 and blurred outside of objects
  * add costmap_generator package
  * add unit tests
  * delete test launch file
  * Apply  ros clang
  * Add README
  * sync develop's readme
  * sync develop's code
  * add convex hull costmap
  * Relay ros header appropriately
  * change interaface for generating costmap from points
  * add test for points2costmap
  * Modify for how to pick up convex-hull points
  * add test
  * add test for objects2costmap
  * Added missing include
  * Added missing grid_map_ros dependency
  * Updated include
  * Re-strutured include folders
  * Generic folder name
  * Fix/costmap generator (`#2077 <https://github.com/CPFL//issues/2077>`_)
  * segmentation fault in  CheckAssignPoints2GridCell
  * Remove redundant codes in test
  * Add some variables in SetUp
  * rename class
  * rename files
  * modify tests
  * Add scription in SetUp
  * Remove unnecesary in_object
  * Refactor test codes
* [fix] Install commands for all the packages (`#1861 <https://github.com/CPFL//issues/1861>`_)
  * Initial fixes to detection, sensing, semantics and utils
  * fixing wrong filename on install command
  * Fixes to install commands
  * Hokuyo fix name
  * Fix obj db
  * Obj db include fixes
  * End of final cleaning sweep
  * Incorrect command order in runtime manager
  * Param tempfile not required by runtime_manager
  * * Fixes to runtime manager install commands
  * Remove devel directory from catkin, if any
  * Updated launch files for robosense
  * Updated robosense
  * Fix/add missing install (`#1977 <https://github.com/CPFL//issues/1977>`_)
  * Added launch install to lidar_kf_contour_track
  * Added install to op_global_planner
  * Added install to way_planner
  * Added install to op_local_planner
  * Added install to op_simulation_package
  * Added install to op_utilities
  * Added install to sync
  * * Improved installation script for pointgrey packages
  * Fixed nodelet error for gmsl cameras
  * USe install space in catkin as well
  * add install to catkin
  * Fix install directives (`#1990 <https://github.com/CPFL//issues/1990>`_)
  * Fixed installation path
  * Fixed params installation path
  * Fixed cfg installation path
  * Delete cache on colcon_release
* Fix license notice in corresponding package.xml
* Contributors: Abraham Monrroy Cano, Kosuke Murakami, amc-nu

1.10.0 (2019-01-17)
-------------------
* Fixes for catkin_make
* Switch to Apache 2 license (develop branch) (`#1741 <https://github.com/CPFL//issues/1741>`_)
  * Switch to Apache 2
  * Replace BSD-3 license header with Apache 2 and reassign copyright to the
   Foundation.
  * Update license on Python files
  * Update copyright years
  * Add #ifndef/define _POINTS_IMAGE_H\_
  * Updated license comment
* Use colcon as the build tool (`#1704 <https://github.com/CPFL//issues/1704>`_)
  * Switch to colcon as the build tool instead of catkin
  * Added cmake-target
  * Added note about the second colcon call
  * Added warning about catkin* scripts being deprecated
  * Fix COLCON_OPTS
  * Added install targets
  * Update Docker image tags
  * Message packages fixes
  * Fix missing dependency
* Add grid_map_vizualization to run_depend
* Feature/perception visualization cleanup (`#1648 <https://github.com/CPFL//issues/1648>`_)
  * * Initial commit for visualization package
  * Removal of all visualization messages from perception nodes
  * Visualization dependency removal
  * Launch file modification
  * * Fixes to visualization
  * Error on Clustering CPU
  * Reduce verbosity on markers
  * intial commit
  * * Changed to 2 spaces indentation
  * Added README
  * Fixed README messages type
  * 2 space indenting
  * ros clang format
  * Publish acceleration and velocity from ukf tracker
  * Remove hardcoded path
  * Updated README
  * updated prototype
  * Prototype update for header and usage
  * Removed unknown label from being reported
  * Updated publishing orientation to match develop
  * * Published all the trackers
  * Added valid field for visualization and future compatibility with ADAS ROI filtering
  * Add simple functions
  * Refacor code
  * * Reversed back UKF node to develop
  * Formatted speed
  * Refactor codes
  * Refactor codes
  * Refactor codes
  * Refacor codes
  * Make tracking visualization work
  * Relay class info in tracker node
  * Remove dependency to jskbbox and rosmarker in ukf tracker
  * apply rosclang to ukf tracker
  * Refactor codes
  * Refactor codes
  * add comment
  * refactor codes
  * Revert "Refactor codes"
  This reverts commit 135aaac46e49cb18d9b76611576747efab3caf9c.
  * Revert "apply rosclang to ukf tracker"
  This reverts commit 4f8d1cb5c8263a491f92ae5321e5080cb34b7b9c.
  * Revert "Remove dependency to jskbbox and rosmarker in ukf tracker"
  This reverts commit 4fa1dd40ba58065f7afacc5e478001078925b27d.
  * Revert "Relay class info in tracker node"
  This reverts commit 1637baac44c8d3d414cc069f3af12a79770439ae.
  * delete dependency to jsk and remove pointcloud_frame
  * get direction nis
  * set velocity_reliable true in tracker node
  * Add divided function
  * add function
  * Sanity checks
  * Relay all the data from input DetectedObject
  * Divided function work both for immukf and sukf
  * Add comment
  * Refactor codes
  * Pass immukf test
  * make direction assisted tracking work
  * Visualization fixes
  * Refacor codes
  * Refactor codes
  * Refactor codes
  * refactor codes
  * refactor codes
  * Refactor codes
  * refactor codes
  * Tracker Merging step added
  * Added launch file support for merging phase
  * lane assisted with sukf
  * Refactor codes
  * Refactor codes
  * * change only static objects
  * keep label of the oldest tracker
  * Static Object discrimination
  * Non rotating bouding box
  * no disappear if detector works
  * Modify removeRedundant a bit
  * Replacement of JSK visualization for RViz Native Markers
  * Added Models namespace to visualization
  * Naming change for matching the perception component graph
  * * Added 3D Models for different classes in visualization
  * 2D Rect node visualize_rects added to visualization_package
* Fix Ros/ROS naming convention
* Contributors: Abraham Monrroy Cano, Akihito Ohsato, Esteve Fernandez, amc-nu

1.9.1 (2018-11-06)
------------------

1.9.0 (2018-10-31)
------------------

1.8.0 (2018-08-31)
------------------
* [Fix] Moved C++11 flag to build_flags (`#1395 <https://github.com/CPFL//pull/1395>`_)
* [Feature] Makes sure that all binaries have their dependencies linked (`#1385 <https://github.com/CPFL//pull/1385>`_)
* [Fix] Extend and Update interface.yaml (`#1291 <https://github.com/CPFL//pull/1291>`_)
* Contributors: Esteve Fernandez, Kenji Funaoka

1.7.0 (2018-05-18)
------------------
* update Version from 1.6.3 to 1.7.0 in package.xml and CHANGELOG.rst
* [fix] Fixes for all packages and dependencies (`#1240 <https://github.com/CPFL//pull/1240>`_)
  * Initial Cleanup
  * fixed also for indigo
  * kf cjeck
  * Fix road wizard
  * Added travis ci
  * Trigger CI
  * Fixes to cv_tracker and lidar_tracker cmake
  * Fix kitti player dependencies
  * Removed unnecessary dependencies
  * messages fixing for can
  * Update build script travis
  * Travis Path
  * Travis Paths fix
  * Travis test
  * Eigen checks
  * removed unnecessary dependencies
  * Eigen Detection
  * Job number reduced
  * Eigen3 more fixes
  * More Eigen3
  * Even more Eigen
  * find package cmake modules included
  * More fixes to cmake modules
  * Removed non ros dependency
  * Enable industrial_ci for indidog and kinetic
  * Wrong install command
  * fix rviz_plugin install
  * FastVirtualScan fix
  * Fix Qt5 Fastvirtualscan
  * Fixed qt5 system dependencies for rosdep
  * NDT TKU Fix catkin not pacakged
  * More in detail dependencies fixes for more packages
  * GLEW library for ORB
  * Ignore OrbLocalizer
  * Ignore Version checker
  * Fix for driveworks interface
  * driveworks not catkinpackagedd
  * Missing catkin for driveworks
  * libdpm opencv not catkin packaged
  * catkin lib gnss  not included in obj_db
  * Points2Polygon fix
  * More missing dependencies
  * image viewer not packaged
  * Fixed SSH2 detection, added viewers for all distros
  * Fix gnss localizer incorrect dependency config
  * Fixes to multiple packages dependencies
  * gnss plib and package
  * More fixes to gnss
  * gnss dependencies for gnss_loclaizer
  * Missing gnss dependency for gnss on localizer
  * More fixes for dependencies
  Replaced gnss for _gnss_library
  * gnss more fixes
  * fixes to more dependencies
  * header dependency
  * Debug message
  * more debug messages changed back to gnss
  * debud messages
  * gnss test
  * gnss install command
  * Several fixes for OpenPlanner and its lbiraries
  * Fixes to ROSInterface
  * More fixes to robotsdk and rosinterface
  * robotsdk calibration fix
  * Fixes to rosinterface robotsdk libraries and its nodes
  * Fixes to Qt5 missing dependencies in robotsdk
  * glviewer missing dependencies
  * Missing qt specific config cmake for robotsdk
  * disable cv_tracker
  * Fix to open planner un needed dependendecies
  * Fixes for libraries indecision maker
  * Fixes to libraries decision_maker installation
  * Gazebo on Kinetic
  * Added Missing library
  * * Removed Gazebo and synchonization packages
  * Renames vmap in lane_planner
  * Added installation commands for missing pakcages
  * Fixes to lane_planner
  * Added NDT TKU Glut extra dependencies
  * ndt localizer/lib fast pcl fixes
  re enable cv_tracker
  * Fix kf_lib
  * Keep industrial_ci
  * Fixes for dpm library
  * Fusion lib fixed
  * dpm and fusion header should match exported project name
  * Fixes to dpm_ocv  ndt_localizer and pcl_omp
  * no fast_pcl anymore
  * fixes to libdpm and its package
  * CI test
  * test with native travis ci
  * missing update for apt
  * Fixes to pcl_omp installation and headers
  * Final fixes for tests, modified README
  * * Fixes to README
  * Enable industrial_ci
  * re enable native travis tests
* Fix/cmake cleanup (`#1156 <https://github.com/CPFL//pull/1156>`_)
  * Initial Cleanup
  * fixed also for indigo
  * kf cjeck
  * Fix road wizard
  * Added travis ci
  * Trigger CI
  * Fixes to cv_tracker and lidar_tracker cmake
  * Fix kitti player dependencies
  * Removed unnecessary dependencies
  * messages fixing for can
  * Update build script travis
  * Travis Path
  * Travis Paths fix
  * Travis test
  * Eigen checks
  * removed unnecessary dependencies
  * Eigen Detection
  * Job number reduced
  * Eigen3 more fixes
  * More Eigen3
  * Even more Eigen
  * find package cmake modules included
  * More fixes to cmake modules
  * Removed non ros dependency
  * Enable industrial_ci for indidog and kinetic
  * Wrong install command
  * fix rviz_plugin install
  * FastVirtualScan fix
  * Fix Qt5 Fastvirtualscan
  * Fixed qt5 system dependencies for rosdep
  * NDT TKU Fix catkin not pacakged
  * Fixes from industrial_ci
* Editorial changes to README files. See `#1124 <https://github.com/CPFL//pull/1124>`_. (`#1125 <https://github.com/CPFL//pull/1125>`_)
* Feature/occupancygrid filter (`#1002 <https://github.com/CPFL//pull/1002>`_)
  * Add grid map filter node
  * Add wayarea2grid node
  * Replace dist_transform with grid_map_filter
  * Add Runtime Manager UI for grid_map_filter node
  * Add UI for wayarea2grid node
  * Add instruction videos
  * Fix videos
  * Both node handles were private
  * Added Comments Documentation
  Code refactoring to follow standards
  Added libraries
  Separation of Vectormap Processing inside Clustering
  * Added documentation
  * Changed variable name
  * Added Road Occupancy Processor package
  * Added extra documentation
  Added commands to RunTimeManager
* Contributors: Abraham Monrroy, David, Kosuke Murakami, TomohitoAndo

1.6.3 (2018-03-06)
------------------

1.6.2 (2018-02-27)
------------------
* Update CHANGELOG
* Contributors: Yusuke FUJII

1.6.1 (2018-01-20)
------------------
* update CHANGELOG
* Contributors: Yusuke FUJII

1.6.0 (2017-12-11)
------------------
* Prepare release for 1.6.0
* Contributors: Yamato ANDO

1.5.1 (2017-09-25)
------------------
* Release/1.5.1 (`#816 <https://github.com/cpfl//issues/816>`_)
  * fix a build error by gcc version
  * fix build error for older indigo version
  * update changelog for v1.5.1
  * 1.5.1
* Contributors: Yusuke FUJII

1.5.0 (2017-09-21)
------------------
* Update changelog
* Add feature to put simulated obstacles in astar planner
* Contributors: TomohitoAndo, Yusuke FUJII

1.4.0 (2017-08-04)
------------------
* version number must equal current release number so we can start releasing in the future
* added changelogs
* Contributors: Dejan Pangercic

1.3.1 (2017-07-16)
------------------

1.3.0 (2017-07-14)
------------------
* Add map offset parameters to points2costmap node
* Add dist_transform node
* convert to auto_msgs
* Contributors: TomohitoAndo, YamatoAndo

1.2.0 (2017-06-07)
------------------
* fixed build issues
* fix circular-dependency
* Update potential field
* Contributors: Shohei Fujii, Yukihiro Saito, Yusuke FUJII

1.1.2 (2017-02-27 23:10)
------------------------

1.1.1 (2017-02-27 22:25)
------------------------

1.1.0 (2017-02-24)
------------------
* Add missing dependency
* Change to use final object topic in potential field
* Cleaned potential field node
* Add vscan points in potential field
* Add potential field package
* Contributors: Yukihiro Saito, h_ohta

1.0.1 (2017-01-14)
------------------

1.0.0 (2016-12-22)
------------------
* Add parameter for subscribing topic
* Fix costmap orientation
* Fix to subscribe the new topic
* Ignore 0 ranges
* Fix cost calculation for unknown costs
* Change variable name to be easier to understand
* Fix calculation of index
* Remove needless nesting
* Modify calculation for costs
* Remove needless compiling flags
* Fix dependencies
* Remove unused header
* Initialize a previous position when declared
* Change variable type from integer to bool
* Impletement some functions as struct method
* Use call by value instead of call by reference with premitive data types
* Add license statement
* Remeve automatically generated comments
* Add semantics package
* Contributors: Syohei YOSHIDA, TomohitoAndo
