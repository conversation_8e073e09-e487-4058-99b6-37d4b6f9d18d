<!-- Launch file for naive_motion_predict -->
<launch>
  <arg name="namespace" default="prediction"/>
  <arg name="input_topic" default="input_objects"/>
  <arg name="output_object_topic" default="objects"/>
  <arg name="output_path_topic" default="path_markers"/>

  <arg name="interval_sec" default="0.1"/>
  <arg name="num_prediction" default="10"/>
  <arg name="sensor_height" default="2.0"/>
  <arg name="filter_out_close_object_threshold" default="1.5"/>

  <group ns="$(arg namespace)">
    <remap from="input_objects" to="$(arg input_topic)"/>
    <remap from="objects" to="$(arg output_object_topic)"/>
    <remap from="path_markers" to="$(arg output_path_topic)"/>

    <node pkg="naive_motion_predict" type="naive_motion_predict" name="naive_motion_predict">
      <param name="interval_sec" value="$(arg interval_sec)"/>
      <param name="num_prediction" value="$(arg num_prediction)"/>
      <param name="sensor_height" value="$(arg sensor_height)"/>
      <param name="filter_out_close_object_threshold" value="$(arg filter_out_close_object_threshold)"/>
    </node>

    <node pkg="detected_objects_visualizer" type="visualize_detected_objects" name="naive_prediction_visualization"/>
  </group>
</launch>
