<?xml version="1.0"?>
<package format="2">
  <name>lanelet2_extension</name>
  <version>1.12.0</version>
  <description>The lanelet2_extension pacakge contains libraries to handle Lanelet2 format data.</description>

  <maintainer email="<EMAIL>">mitsudome-r</maintainer>

  <license>Apach 2</license>

  <buildtool_depend>build_flags</buildtool_depend>
  <buildtool_depend>catkin</buildtool_depend>

  <depend>amathutils_lib</depend>
  <depend>roscpp</depend>
  <depend>geographiclib</depend>
  <depend>lanelet2_core</depend>
  <depend>lanelet2_io</depend>
  <depend>lanelet2_maps</depend>
  <depend>lanelet2_projection</depend>
  <depend>lanelet2_routing</depend>
  <depend>lanelet2_traffic_rules</depend>
  <depend>lanelet2_validation</depend>
  <depend>lanelet2_msgs</depend>
  <depend>auto_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>visualization_msgs</depend>
  <depend>pugixml-dev</depend>
  <depend>roslint</depend>
</package>
