<?xml version="1.0"?>
<launch>
  <node pkg="op_local_planner" type="op_common_params" name="op_common_params" output="screen">

    <!-- Common Parameters -->
    <rosparam command="load" file="$(find ins)/config/map.yaml" />
    <param name="mapSource" value="1" />
    <!-- Autoware=0, Vector Map Folder=1, kml=2 -->
    <!-- <param name="mapFileName" value="$(arg mapFileName)" /> -->

    <!-- 控制路径点的密度，影响路径的计算和展开 -->
    <param name="pathDensity" value="0.5" />
    <!-- 定义路径展开的数量，用于决策过程中考虑的不同路径选项 -->
    <param name="rollOutDensity" value="0.5" />
    <!-- 定义路径展开的数量，用于决策过程中考虑的不同路径选项 -->
    <param name="rollOutsNumber" value="6" />

    <!-- 设置车辆的最大和最小速度 -->
    <param name="maxVelocity" value="1.38888" />
    <param name="minVelocity" value="0.83333" />
    <!-- 设置局部规划的最大距离 -->
    <param name="maxLocalPlanDistance" value="30" />
    <!-- 规定路径规划考虑的前向距离 -->
    <param name="horizonDistance" value="70" />

    <!-- 定义车辆在跟随和避障时应保持的最小距离 -->
    <param name="minFollowingDistance" value="2.0" />
    <!-- should be bigger than Distance to follow -->
    <param name="minDistanceToAvoid" value="1.5" />
    <!-- should be smaller than minFollowingDistance and larger than maxDistanceToAvoid -->
    <!-- 设置避障时的最大距离-->
    <param name="maxDistanceToAvoid" value="0.5" />
    <!-- should be smaller than minDistanceToAvoid -->
    <!-- 调整速度剖面，影响路径点的速度调整程度 -->
    <param name="speedProfileFactor" value="1.1111111" />


    <!-- weight_data（对原始路径点的依赖程度）：
• 当 weight_data 值较高时，平滑后的路径会更加接近原始路径这意味着算法会在尊重原始路径数据点的基础上进行少量的调整
• 如果 weight_data 值较低，算法对原始数据点的依赖减少，平滑后的路径可能会更加偏离原始路径，特别是在路径中的转弯或弯道部分 -->
    <param name="smoothingDataWeight" value="0.65" />
    <!-- weight_smooth（相邻点之间的平滑程度）：
• 当 weight_smooth 值较高时，算法更强调平滑性，使得路径中的转弯更加平滑，减少尖锐角度这对于避免路径中的急转弯很有帮助
• 如果 weight_smooth 值较低，平滑处理的影响减少，路径可能会保留更多原始路径的尖锐转弯或不规则性 -->
    <param name="smoothingSmoothWeight" value="0.2" />

    <!-- 定义车辆的水平和垂直安全距离，以确保安全行驶 -->
    <param name="horizontalSafetyDistance" value="0.2" />
    <param name="verticalSafetyDistance" value="0.3" />

    <!-- 开关参数，用于启用或禁用特定的驾驶行为，如避障、跟随、交通灯行为、停车标志行为和换道 -->
    <param name="enableSwerving" value="false" />
    <param name="enableFollowing" value="true" />
    <param name="enableTrafficLightBehavior" value="false" />
    <param name="enableStopSignBehavior" value="false" />
    <param name="enableLaneChange" value="false" />

    <!-- 设置车辆的物理尺寸和转向能力 -->
    <param name="width" value="1.2" />
    <param name="length" value="1.85" />
    <param name="wheelBaseLength" value="1.2" />
    <param name="turningRadius" value="3" />
    <param name="maxSteerAngle" value="0.489" /><!-- 28 degree-->

    <!-- 控制车辆的驾驶响应，如转向延迟、追踪距离和额外的刹车距离 -->
    <param name="steeringDelay" value="1.2" />
    <param name="minPursuiteDistance" value="2.0" />
    <param name="additionalBrakingDistance" value="4.0" />

    <!-- 设置在某些情况下车辆应放弃跟随或避障的距离 -->
    <param name="giveUpDistance" value="-4.0" />

    <!-- 控制车辆的加速和减速性能 -->
    <param name="maxAcceleration" value="3.0" />
    <param name="maxDeceleration" value="-2.0" />

    <param name="velocitySource" value="1" />
    <!-- read velocities from (0- Odometry, 1- autoware current_velocities, 2- car_info) "" -->

  </node>

  <node pkg="op_local_planner" type="op_motion_predictor" name="op_motion_predictor" output="screen">

    <!-- 参数用于设置车辆与车道线的最大允许距离，超出此值可能导致车辆偏离车道 -->
    <param name="max_distance_to_lane" value="1.0" />
    <!-- 预测距离参数设定了车辆前方运动预测的范围，较大值可增强前方障碍物的预测能力 -->
    <param name="prediction_distance" value="25.0" />
    <!-- 启用此选项可以生成导航路径的分支，适用于复杂的路网环境 -->
    <param name="enableGenrateBranches" value="false" />
    <!-- 启用此选项使车辆能够识别并响应路边的障碍物 -->
    <param name="enableCurbObstacles" value="false" />
    <!-- 设定车辆识别路边障碍物之间的距离，用于导航和障碍物规避 -->
    <param name="distanceBetweenCurbs" value="1.5" />
    <!-- 控制可视化元素的更新频率，较低的值可以使动态显示更流畅 -->
    <param name="visualizationTime" value="0.25" />
    <!-- 若启用，车辆将逐步执行并发出信号，有助于调试和细节跟踪 -->
    <param name="enableStepByStepSignal" value="false" />
    <!-- 启用粒子滤波预测可提高车辆在环境中的定位精度，尤其在环境不确定性较高时 -->
    <param name="enableParticleFilterPrediction" value="false" />

  </node>

  <node pkg="op_local_planner" type="op_behavior_selector" name="op_behavior_selector"
    output="screen">
    <!--
    此参数设置行为选择器用于评估不同驾驶行为可行性时考虑的证据信任度。增大这个数值可能会使系统对证据更加严格，
    只有当证据非常充分时才会改变驾驶行为，从而可能减少错误行为的选择但增加响应时间。反之，
    减小这个数值会使系统对证据的要求降低，提高响应速度但可能增加错误判断的风险。 -->
    <param name="evidence_tust_number" value="25" />
  </node>

  <node pkg="op_local_planner" type="op_trajectory_evaluator" name="op_trajectory_evaluator"
    output="screen">
    <!-- 启用或禁用路径预测功能，用于在路径评估过程中考虑潜在的未来位置 -->
    <param name="enablePrediction" value="false" />
    <!-- 水平安全距离定义了车辆与其它对象在水平方向上的最小安全距离 -->
    <param name="horizontalSafetyDistance" value="0.2" />
    <!-- 垂直安全距离定义了车辆与其它对象在垂直方向上的最小安全距离 -->
    <param name="verticalSafetyDistance" value="1.2" />
  </node>

  <node pkg="op_local_planner" type="op_trajectory_generator" name="op_trajectory_generator"
    output="screen">
    <!-- 'samplingTipMargin' 定义了在生成轨迹时，车辆当前位置向前的距离，影响车辆计划的前瞻性。 -->
    <param name="samplingTipMargin" value="4" />
    <!-- 'samplingOutMargin' 设置在轨迹生成期间，车辆两侧考虑的横向距离，影响规划窗口的宽度。 -->
    <param name="samplingOutMargin" value="16" />
    <!-- 'samplingSpeedFactor' 影响采样过程中速度的缩放，根据车速影响轨迹生成。 -->
    <param name="samplingSpeedFactor" value="0.25" />
    <!-- 'enableHeadingSmoothing' 切换是否应用航向平滑，这可以使车辆的路径更平滑、更稳定，但可能以响应速度为代价。 -->
    <param name="enableHeadingSmoothing" value="false" />

  </node>

  <node pkg="waypoint_maker" type="waypoint_marker_publisher" name="waypoint_marker_publisher" />
  <arg name="use_ll2" default="false" />

  <node if="$(arg use_ll2)" pkg="lane_planner" type="lane_rule_lanelet2" name="lane_rule"
    output="screen" />
  <node unless="$(arg use_ll2)" pkg="lane_planner" type="lane_rule" name="lane_rule" output="screen" />

</launch>