#include "pose.h"
#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>

void publishInitialPose()
{
    ros::NodeHandle nh;
    //发布定位坐标
    ros::Publisher pub = nh.advertise<geometry_msgs::PoseWithCovarianceStamped>("/initialpose", 10);
    geometry_msgs::PoseWithCovarianceStamped ps;
    ps.pose.pose.position.x = 0.460661917925;
    ps.pose.pose.position.y = -2.1728105545;
    ps.pose.pose.position.z = 0;
    ps.pose.pose.orientation.x = 0;
    ps.pose.pose.orientation.y = 0;
    ps.pose.pose.orientation.z = -0.00434742239367;
    ps.pose.pose.orientation.w = 0.999981136379;
    ps.header.stamp = ros::Time::now();
    ps.header.frame_id = "world"; // Set the frame ID according to your needs

    int count = 0;
    while (count < 5)
    {
        pub.publish(ps);
        ros::spinOnce(); // Allow ROS to process the message publishing
        count++;
        ros::Duration(0.1).sleep(); // Add a short delay between each message publishing
    }

    // 发布任务点坐标
    //  ros::Publisher pub = nh.advertise<geometry_msgs::PoseStamped>("/move_base_simple/goal", 10);
    //  geometry_msgs::PoseStamped ps;
    //  ps.pose.position.x = 8.70847988129;
    //  ps.pose.position.y = -7.61960983276;
    //  ps.pose.position.z = 0;
    //  ps.pose.orientation.x = 0;
    //  ps.pose.orientation.y = 0;
    //  ps.pose.orientation.z = -0.717572959728;
    //  ps.pose.orientation.w = 0.696483343281;

    // pub.publish(ps);
    // ros::shutdown(); // 发布一次后关闭节点
}