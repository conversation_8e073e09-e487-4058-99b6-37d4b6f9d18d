<launch>
  <!-- 原有参数 -->
  <arg name="coord_system" default="0" />  <!-- 0-WGS84, 1-CGC2000 -->
  <arg name="use_fixed_origin" default="false" />
  <arg name="origin_lat" default="0.0" />
  <arg name="origin_lon" default="0.0" />
  <arg name="origin_alt" default="0.0" />
  
  <!-- 新增UTM原点参数 -->
  <arg name="use_utm_origin" default="true" />
  <arg name="origin_utm_x" default="624226.839217" />
  <arg name="origin_utm_y" default="3253781.897591" />
  <arg name="origin_utm_z" default="315.151306" />  <!-- 添加UTM坐标的高度参数 -->
  <arg name="antanna_2_base_link" default="3.4" />
  
  <node pkg="gnss_localizer" type="fix2tfpose" name="fix2tfpose" output="screen">
    <param name="coord_system" value="$(arg coord_system)" />
    <param name="use_fixed_origin" value="$(arg use_fixed_origin)" />
    <param name="origin_lat" value="$(arg origin_lat)" />
    <param name="origin_lon" value="$(arg origin_lon)" />
    <param name="origin_alt" value="$(arg origin_alt)" />
    
    <!-- 传递UTM原点参数 -->
    <param name="use_utm_origin" value="$(arg use_utm_origin)" />
    <param name="origin_utm_x" value="$(arg origin_utm_x)" />
    <param name="origin_utm_y" value="$(arg origin_utm_y)" />
    <param name="origin_utm_z" value="$(arg origin_utm_z)" />  <!-- 传递UTM高度参数 -->
  </node>
</launch>