#pragma once
#include <usbcan/can.h>
namespace can_msg
{
    /* huace_gps */
    /* id: 0x32A */
    class Yaw : public can::CanData
    {
    public:
        Yaw() : can::CanData(8) {}
        explicit Yaw(unsigned char *data) : can::CanData(data, 8) {}
        float AngleHeading() const
        {
            return get<uint16_t>(8, 16, BIG_ENDIAN) * static_cast<float>(0.01) + static_cast<float>(0);
        }
    };

    /* huace_gps */
    /* id: 0x324 */
    class GPS_msg : public can::CanData
    {
    public:
        GPS_msg() : can::CanData(8) {}
        explicit GPS_msg(unsigned char *data) : can::CanData(data, 8) {}
        float PosLat() const
        {
            return get<int32_t>(24, 32, BIG_ENDIAN) * static_cast<float>(1e-7) + static_cast<float>(0);
        }
        float PosLon() const
        {
            return get<int32_t>(56, 32, B<PERSON>_ENDIAN) * static_cast<float>(1e-7) + static_cast<float>(0);
        }
    };

    /* daoyuan_gps */
    /* id: 0x502 */
    class YawD : public can::CanData
    {
    public:
        YawD() : can::CanData(8) {}
        explicit YawD(unsigned char *data) : can::CanData(data, 8) {}
        float AngleHeading() const
        {
            return get<uint16_t>(40, 16, BIG_ENDIAN) * static_cast<float>(0.010986) - static_cast<float>(360);
        }
    };

    /* daoyuan_gps */
    /* id: 0x504 */
    class GPS_msgD : public can::CanData
    {
    public:
        GPS_msgD() : can::CanData(8) {}
        explicit GPS_msgD(unsigned char *data) : can::CanData(data, 8) {}
        float PosLat() const
        {
            return get<int32_t>(24, 32, BIG_ENDIAN) * static_cast<float>(1e-7) - static_cast<float>(180);
        }
        float PosLon() const
        {
            return get<int32_t>(56, 32, BIG_ENDIAN) * static_cast<float>(1e-7) - static_cast<float>(180);
        }
    };

    /* None */
    /* id: 0x283 */
    class vechicle_control : public can::CanData
    {
    public:
        vechicle_control() : can::CanData(8) {}
        explicit vechicle_control(const unsigned char *data) : can::CanData(data, 8) {}
        char control_mode() const
        {
            return get<uint8_t>(24, 1, LITTLE_ENDIAN);
        }
    };

    /* None */
    /* id: 0x285 */
    class DJ2_control : public can::CanData
    {
    public:
        DJ2_control() : can::CanData(8) {}
        explicit DJ2_control(const unsigned char *data) : can::CanData(data, 8) {}
        void speed_value(float value)
        {
            set<uint16_t>(0, 16, LITTLE_ENDIAN, value);
        }
        void break_value(float value)
        {
            set<uint16_t>(16, 16, LITTLE_ENDIAN, value);
        }
        void left_value(float value)
        {
            set<uint16_t>(32, 16, LITTLE_ENDIAN, value);
        }
        void right_value(float value)
        {
            set<uint16_t>(48, 16, LITTLE_ENDIAN, value);
        }
    };

    /* 电机反馈 */
    /* id: 287 */
    class DJ2_speed : public can::CanData
    {
    public:
        DJ2_speed() : can::CanData(8) {}
        explicit DJ2_speed(const unsigned char *data) : can::CanData(data, 8) {}
        int state() const
        {
            return get<int32_t>(0, 2, LITTLE_ENDIAN) * static_cast<float>(1) - static_cast<float>(0);
        }
        int speed_mode() const
        {
            return get<int32_t>(2, 1, LITTLE_ENDIAN) * static_cast<float>(1) - static_cast<float>(0);
        }
        float speed() const
        {
            return get<uint16_t>(8, 16, LITTLE_ENDIAN) * static_cast<float>(1) - static_cast<float>(0);
        }
        int erro_state() const
        {
            return get<int32_t>(24, 8, LITTLE_ENDIAN) * static_cast<float>(1) - static_cast<float>(0);
        }
        int battery_soc_() const
        {
            return get<int32_t>(56, 8, LITTLE_ENDIAN) * static_cast<float>(0.01) - static_cast<float>(0);
        }
    };

    /* 电池 */
    /* id: 0x288 */
    class DJ2_BMS : public can::CanData
    {
    public:
        DJ2_BMS() : can::CanData(8) {}
        explicit DJ2_BMS(const unsigned char *data) : can::CanData(data, 8) {}
        int YCB_SOC() const
        {
            return get<uint>(0, 8, LITTLE_ENDIAN) * static_cast<float>(0.4) - static_cast<float>(0);
        }
    };

    /* None */
    /* id: 0x101 */
    class DJ2_light : public can::CanData
    {
    public:
        DJ2_light() : can::CanData(8) {}
        explicit DJ2_light(unsigned char *data) : can::CanData(data, 8) {}
        void Red_light(int value)
        {
            set<uint8_t>(0, 1, LITTLE_ENDIAN, value);
        }
        void Yellow_light(int value)
        {
            set<uint8_t>(1, 1, LITTLE_ENDIAN, value);
        }
        void Green_light(int value)
        {
            set<uint8_t>(2, 1, LITTLE_ENDIAN, value);
        }
        void Buzzer(int value)
        {
            set<uint8_t>(3, 1, LITTLE_ENDIAN, value);
        }
        void Hook_up(int value)
        {
            set<uint8_t>(4, 1, LITTLE_ENDIAN, value);
        }
        void Hook_down(int value)
        {
            set<uint8_t>(5, 1, LITTLE_ENDIAN, value);
        }
    };

    /* None */
    /* id: 0x102 */
    class DJ2_voice : public can::CanData
    {
    public:
        DJ2_voice() : can::CanData(8) {}
        explicit DJ2_voice(unsigned char *data) : can::CanData(data, 8) {}
        void turn_right_voice(int value)
        {
            set<uint8_t>(4, 1, LITTLE_ENDIAN, value);
        }
        void turn_left_voice(int value)
        {
            set<uint8_t>(5, 1, LITTLE_ENDIAN, value);
        }
        void brake_voice(int value)
        {
            set<uint8_t>(6, 1, LITTLE_ENDIAN, value);
        }
        void reverse_voice(int value)
        {
            set<uint8_t>(7, 1, LITTLE_ENDIAN, value);
        }
    };

    class button : public can::CanData
    {
    public:
        button() : can::CanData(8) {}
        explicit button(const unsigned char *data) : can::CanData(data, 8) {}
        int button_data() const
        {
            return get<uint8_t>(0, 8, LITTLE_ENDIAN) * static_cast<float>(1) - static_cast<float>(0);
        }
    };

    /*------------------------超声波报文解析------------------------*/
    class detection_distance : public can::CanData
    {
    public:
        detection_distance() : can::CanData(8) {}
        explicit detection_distance(unsigned char *data) : can::CanData(data, 8) {}
        float detection1() const
        {
            return get<uint16_t>(0, 8, LITTLE_ENDIAN) * static_cast<float>(2) + static_cast<float>(0);
        }
        float detection2() const
        {
            return get<uint16_t>(8, 8, LITTLE_ENDIAN) * static_cast<float>(2) + static_cast<float>(0);
        }
        float detection3() const
        {
            return get<uint16_t>(16, 8, LITTLE_ENDIAN) * static_cast<float>(2) + static_cast<float>(0);
        }
        float detection4() const
        {
            return get<uint16_t>(24, 8, LITTLE_ENDIAN) * static_cast<float>(2) + static_cast<float>(0);
        }
    };

};