<launch>
    <arg name="output" default="log"/>
    <node pkg="system_health_checker" type="health_aggregator" name="health_aggregator" output="$(arg output)" respawn="true" respawn_delay="0" />

    <!-- vel_pose_diff_checker -->
    <arg name="input_pose_name" default="/ndt_pose"/>
    <arg name="input_twist_name" default="/estimate_twist"/>

    <arg name="loop_rate_hz" default="20.0"/>
    <arg name="comparison_window_size_sec" default="1.0"/>
    <arg name="moving_median_window_size_sec" default="2.0"/>
    <arg name="topic_timeout_sec" default="0.3"/>
    <arg name="diff_position_threshold_meter" default="1.0"/>
    <arg name="diff_position_median_threshold_meter" default="0.5"/>
    <arg name="diff_angle_threshold_rad" default="0.1"/>
    <arg name="diff_angle_median_threshold_rad" default="0.05"/>

    <rosparam command="load" file="$(find vel_pose_diff_checker)/config/health_checker.yaml" />

    <node pkg="vel_pose_diff_checker" type="vel_pose_diff_checker" name="vel_pose_diff_checker" output="log">
        <!-- Input Topics -->
        <remap from="current_pose" to="$(arg input_pose_name)" />
        <remap from="current_velocity" to="$(arg input_twist_name)" />

        <!-- Params -->
        <param name="loop_rate_hz" value="$(arg loop_rate_hz)" />
        <param name="comparison_window_size_sec" value="$(arg comparison_window_size_sec)" />
        <param name="topic_timeout_sec" value="$(arg topic_timeout_sec)" />
        <param name="moving_median_window_size_sec" value="$(arg moving_median_window_size_sec)" />
        <param name="diff_position_threshold_meter" value="$(arg diff_position_threshold_meter)" />
        <param name="diff_position_median_threshold_meter" value="$(arg diff_position_median_threshold_meter)" />
        <param name="diff_angle_threshold_rad" value="$(arg diff_angle_threshold_rad)" />
        <param name="diff_angle_median_threshold_rad" value="$(arg diff_angle_median_threshold_rad)" />
    </node>

    <!-- ndt_matching_monitor -->
    <arg name="iteration_threshold_warn" default="10" />
    <arg name="iteration_threshold_stop" default="32" />
    <arg name="score_delta_threshold" default="14.0" />
    <arg name="min_stable_samples" default="30.0" />
    <arg name="fatal_time_threshold" default="2.0" />

    <node pkg="lidar_localizer" type="ndt_matching_monitor" name="ndt_matching_monitor" output="screen">
        <param name="/ndt_monitor/iteration_threshold_warn" value="$(arg iteration_threshold_warn)" />
        <param name="/ndt_monitor/iteration_threshold_stop" value="$(arg iteration_threshold_stop)" />
        <param name="/ndt_monitor/score_delta_threshold" value="$(arg score_delta_threshold)" />
        <param name="/ndt_monitor/min_stable_samples" value="$(arg min_stable_samples)" />
        <param name="/ndt_monitor/fatal_time_threshold" value="$(arg fatal_time_threshold)" />
    </node>

    <node pkg="topic_health_monitor" type="topic_health_monitor_node" name="topic_health_monitor_node" output="screen" />
</launch>
