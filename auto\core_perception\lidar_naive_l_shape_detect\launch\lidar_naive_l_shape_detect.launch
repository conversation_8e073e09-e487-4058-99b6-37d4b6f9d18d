<!-- Launch file for L shape box fitting -->
<launch>
  <arg name="namespace" default="detection/l_shape" />
  <arg name="input_topic" default="/detection/lidar_detector/objects" /><!--CHANGE THIS TO READ WHETHER FROM VSCAN OR POINTS_RAW -->
  <arg name="output_topic" default="objects" />
  <arg name="visualize" default="true" />

  <arg name="random_ponts" default="80" />
  <arg name="slope_dist_thres" default="2.0" />
  <arg name="num_points_thres" default="10" />
  <arg name="sensor_height" default="2.35" />

  <group ns="$(arg namespace)">
    <remap from="unbounded_objects" to="$(arg input_topic)" />
    <remap from="objects" to="$(arg output_topic)" />

    <node pkg="lidar_naive_l_shape_detect" type="lidar_naive_l_shape_detect"
      name="lidar_naive_l_shape_detect" output="screen" >

      <param name="random_ponts" value="$(arg random_ponts)" />
      <param name="slope_dist_thres" value="$(arg slope_dist_thres)" />
      <param name="num_points_thres" value="$(arg num_points_thres)" />
      <param name="sensor_height" value="$(arg sensor_height)" />
    </node>

    <node if="$(arg visualize)" pkg="detected_objects_visualizer" type="visualize_detected_objects"
      name="lidar_naive_l_shape_viz" output="screen" />
  </group>

</launch>
