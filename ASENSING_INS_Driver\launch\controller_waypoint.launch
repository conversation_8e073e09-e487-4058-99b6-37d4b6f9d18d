<launch>
    <!-- 是否进行线性插值来计算速度和位姿之间的差异 -->
    <arg name="is_linear_interpolation" default="True"/>
    <!-- 是否为转向机器人发布差异信息 -->
    <arg name="publishes_for_steering_robot" default="True"/>
    <!-- 是否添加虚拟的终点路点 -->
    <arg name="add_virtual_end_waypoints" default="false"/>
    <!-- 前瞻距离 -->
    <arg name="const_lookahead_distance" default="3"/>
    <!-- 设置车辆速度 -->
    <arg name="const_velocity" default="1.0"/>
    <!-- 前瞻点距离与当前速度的比率 -->
    <arg name="lookahead_ratio" default="2.0"/>
    <!--  最小的前瞻点距离-->
    <arg name="minimum_lookahead_distance" default="2"/>

    <!-- 设置速度来源，0：表示速度来源录制速度，1：表示速度来源是提供的恒定速度 (用waypoint才需要设置成0)-->
    <!-- 0 = waypoints, 1 = provided constant velocity -->
    <arg name="velocity_source" default="0"/>

    <!-- rosrun waypoint_follower pure_pursuit -->
    <node pkg="pure_pursuit" type="pure_pursuit" name="pure_pursuit" output="log" respawn="true">
        <param name="is_linear_interpolation" value="$(arg is_linear_interpolation)"/>
        <param name="publishes_for_steering_robot" value="$(arg publishes_for_steering_robot)"/>
        <param name="add_virtual_end_waypoints" value="$(arg add_virtual_end_waypoints)"/>
        <param name="const_lookahead_distance" value="$(arg const_lookahead_distance)"/>
        <param name="const_velocity" value="$(arg const_velocity)"/>
        <param name="lookahead_ratio" value="$(arg lookahead_ratio)"/>
        <param name="minimum_lookahead_distance" value="$(arg minimum_lookahead_distance)"/>
        <param name="velocity_source" value="$(arg velocity_source)"/>
    </node>
    <!-- For twist_filter -->
    <!-- 设置车辆的轴距 -->
    <arg name="wheel_base" default="1.2" />
    <!-- 设置车辆的横向加速度限制 -->
    <arg name="lateral_accel_limit" default="5.0" />
    <!-- 设置车辆的横向加速度变化率限制 -->
    <arg name="lateral_jerk_limit" default="5.0" />
    <!-- 设置线性速度的低通滤波增益 -->
    <arg name="lowpass_gain_linear_x" default="0.0" />
    <!-- 设置角速度的低通滤波增益 -->
    <arg name="lowpass_gain_angular_z" default="0.0" />
    <!-- 设置方向盘转角的低通滤波增益 -->
    <arg name="lowpass_gain_steering_angle" default="0.0" />

    <param name="vehicle_info/wheel_base" value="$(arg wheel_base)" />

    <!-- For twist_gate -->
    <!-- 设置循环频率 -->
    <arg name="loop_rate" default="30.0" />
    <!-- 是否使用决策制定器 -->
    <arg name="use_decision_maker" default="false" />

    <!-- rosrun waypoint_follower twist_filter -->
    <node pkg="twist_filter" type="twist_filter" name="twist_filter" output="log" respawn="true">
        <param name="lateral_accel_limit" value="$(arg lateral_accel_limit)" />
        <param name="lateral_jerk_limit" value="$(arg lateral_jerk_limit)" />
        <param name="lowpass_gain_linear_x" value="$(arg lowpass_gain_linear_x)" />
        <param name="lowpass_gain_angular_z" value="$(arg lowpass_gain_angular_z)" />
        <param name="lowpass_gain_steering_angle" value="$(arg lowpass_gain_steering_angle)" />
    </node>

    <node pkg="twist_gate" type="twist_gate" name="twist_gate" output="log" respawn="true">
        <param name="loop_rate" value="$(arg loop_rate)" />
        <param name="use_decision_maker" value="$(arg use_decision_maker)" />
    </node>
    <!-- usbcan -->
    <node pkg="usbcan" type="usbcan_node" name="myusbcan" required="true" output="screen" />
    <!-- 倒车Purepursuit -->
    <!-- <node pkg="motion_planning" type="hw.py" name="Purepursuit" output="screen"/> -->


    <!-- Relay behavior configurations -->
    <!-- 安全路径点的数量 -->
    <arg name="safety_waypoints_size" default="150" />
    <!-- 更新频率 -->
    <arg name="update_rate" default="10" />

    <!-- Avoidance behavior configurations -->
    <arg name="costmap_topic" default="semantics/costmap_generator/occupancy_grid" />
    <!-- 启用或禁用避障行为 -->
    <arg name="enable_avoidance" default="false" />
    <!-- 设置避障路径点的速度 -->
    <arg name="avoid_waypoints_velocity" default="4" />
    <!-- 设置避障路径的起始速度 -->
    <arg name="avoid_start_velocity" default="3.0" />
    <!-- 设置重新规划路径的时间间隔 -->
    <arg name="replan_interval" default="1.5" />
    <!-- 设置避障路径搜索的路径点数量 -->
    <arg name="search_waypoints_size" default="50" />
    <!-- 设置避障路径搜索的路径点间隔 -->
    <arg name="search_waypoints_delta" default="2" />
    <!-- 设置避障路径搜索的最近路径点数量 -->
    <arg name="closest_search_size" default="20" />

    <!-- A* search configurations -->
    <!-- 是否考虑后退动作作为路径规划的选项 -->
    <arg name="use_back" default="false" />
    <!-- 是否使用潜在场势（potential heuristic）作为启发式函数 -->
    <arg name="use_potential_heuristic" default="true" />
    <!-- 是否使用波前走势（wavefront heuristic）作为启发式函数 -->
    <arg name="use_wavefront_heuristic" default="false" />
    <!-- 搜索的时间限制，单位为毫秒 -->
    <arg name="time_limit" default="1000.0" />
    <!-- 机器人的长度 -->
    <arg name="robot_length" default="1.85" />
    <!-- 机器人的宽度 -->
    <arg name="robot_width" default="1.75" />
    <!-- 机器人的基准点到后背的距离 -->
    <arg name="robot_base2back" default="0.2" />
    <!-- 机器人的最小转弯半径 -->
    <arg name="minimum_turning_radius" default="6.0" />
    <!-- 离散角度空间的分辨率 -->
    <arg name="theta_size" default="48" />
    <!-- 路径曲率的权重 -->
    <arg name="curve_weight" default="2" />
    <!-- 后退动作的权重 -->
    <arg name="reverse_weight" default="2.0" />
    <!-- 横向目标范围 -->
    <arg name="lateral_goal_range" default="2.0" />
    <!-- 纵向目标范围 -->
    <arg name="longitudinal_goal_range" default="2.0" />
    <!-- 角度目标范围 -->
    <arg name="angle_goal_range" default="6.0" />
    <!-- 障碍物的阈值，用于定义障碍物的占用度 -->
    <arg name="obstacle_threshold" default="100" />
    <!-- 潜在场势的权重 -->
    <arg name="potential_weight" default="10.0" />
    <!-- 距离启发式函数的权重 -->
    <arg name="distance_heuristic_weight" default="0.4" />

    <node pkg="waypoint_planner" type="astar_avoid" name="astar_avoid" output="screen">
        <param name="safety_waypoints_size" value="$(arg safety_waypoints_size)" />
        <param name="update_rate" value="$(arg update_rate)" />

        <remap from="costmap" to="$(arg costmap_topic)" />
        <param name="enable_avoidance" value="$(arg enable_avoidance)" />
        <param name="search_waypoints_size" value="$(arg search_waypoints_size)" />
        <param name="search_waypoints_delta" value="$(arg search_waypoints_delta)" />
        <param name="closest_search_size" value="$(arg closest_search_size)" />
        <param name="avoid_waypoints_velocity" value="$(arg avoid_waypoints_velocity)" />
        <param name="avoid_start_velocity" value="$(arg avoid_start_velocity)" />
        <param name="replan_interval" value="$(arg replan_interval)" />

        <param name="use_back" value="$(arg use_back)" />
        <param name="use_potential_heuristic" value="$(arg use_potential_heuristic)" />
        <param name="use_wavefront_heuristic" value="$(arg use_wavefront_heuristic)" />
        <param name="time_limit" value="$(arg time_limit)" />
        <param name="robot_length" value="$(arg robot_length)" />
        <param name="robot_width" value="$(arg robot_width)" />
        <param name="robot_base2back" value="$(arg robot_base2back)" />
        <param name="minimum_turning_radius" value="$(arg minimum_turning_radius)" />
        <param name="theta_size" value="$(arg theta_size)" />
        <param name="angle_goal_range" value="$(arg angle_goal_range)" />
        <param name="curve_weight" value="$(arg curve_weight)" />
        <param name="reverse_weight" value="$(arg reverse_weight)" />
        <param name="lateral_goal_range" value="$(arg lateral_goal_range)" />
        <param name="longitudinal_goal_range" value="$(arg longitudinal_goal_range)" />
        <param name="obstacle_threshold" value="$(arg obstacle_threshold)" />
        <param name="potential_weight" value="$(arg potential_weight)" />
        <param name="distance_heuristic_weight" value="$(arg distance_heuristic_weight)" />
    </node>

    <!-- velocity_set -->
    <!-- 是否启用人行横道检测 -->
    <arg name="use_crosswalk_detection" default="false" />
    <!-- 输入点云数据的话题名称 -->
    <arg name="points_topic" default="/points_no_ground" />
    <!-- 速度偏移量，用于增加或减少期望速度 -->
    <arg name="velocity_offset" default="1.2" />
    <!-- 减速后的最小速度 -->
    <arg name="decelerate_vel_min" default="1.0" />
    <!-- 移除比该距离更近的点云数据 -->
    <arg name="remove_points_upto" default="0.5" />
    <!-- 是否启用多个人行横道的检测 -->
    <arg name="enable_multiple_crosswalk_detection" default="false" />
    <!-- 车辆停止距离 -->
    <arg name="stop_distance_obstacle" default="5.0" />
    <!-- 车辆停止线距离 -->
    <arg name="stop_distance_stopline" default="5.0" />
    <!-- 障碍物检测范围的半径 -->
    <arg name="detection_range" default="0.65" />
    <!-- 点云数据的阈值，小于该值将被过滤 -->
    <arg name="points_threshold" default="10" />
    <!-- 检测的高度上限 -->
    <arg name="detection_height_top" default="1.5" />
    <!-- 检测的高度下限 -->
    <arg name="detection_height_bottom" default="-1.2" />
    <!-- 障碍物减速度 -->
    <arg name="deceleration_obstacle" default="0.6" />
    <!-- 停车线减速度 -->
    <arg name="deceleration_stopline" default="2.0" />
    <!-- 速度变化的限制 -->
    <arg name="velocity_change_limit" default="9.972" />
    <!-- 减速范围，例如设置为10.0，则只在距离障碍物10米以内的范围内进行减速 -->
    <arg name="deceleration_range" default="0" />
    <!-- 暂存的路径点数量 -->
    <arg name="temporal_waypoints_size" default="70" />

    <node pkg="waypoint_planner" type="velocity_set" name="velocity_set" output="screen">
        <param name="use_crosswalk_detection" value="$(arg use_crosswalk_detection)" />
        <param name="enable_multiple_crosswalk_detection" value="$(arg enable_multiple_crosswalk_detection)" />
        <param name="points_topic" value="$(arg points_topic)" />
        <param name="velocity_offset" value="$(arg velocity_offset)" />
        <param name="decelerate_vel_min" value="$(arg decelerate_vel_min)" />
        <param name="remove_points_upto" value="$(arg remove_points_upto)" />
        <param name="stop_distance_obstacle" value="$(arg stop_distance_obstacle)" />
        <param name="stop_distance_stopline" value="$(arg stop_distance_stopline)" />
        <param name="detection_range" value="$(arg detection_range)" />
        <param name="points_threshold" value="$(arg points_threshold)" />
        <param name="detection_height_top" value="$(arg detection_height_top)" />
        <param name="detection_height_bottom" value="$(arg detection_height_bottom)" />
        <param name="deceleration_obstacle" value="$(arg deceleration_obstacle)" />
        <param name="deceleration_stopline" value="$(arg deceleration_stopline)" />
        <param name="velocity_change_limit" value="$(arg velocity_change_limit)" />
        <param name="deceleration_range" value="$(arg deceleration_range)" />
        <param name="temporal_waypoints_size" value="$(arg temporal_waypoints_size)" />
    </node>

</launch>
