<!-- -->
<launch>
  <arg name="points_node" default="/points_raw" /><!--CHANGE THIS TO READ WHETHER FROM VSCAN OR POINTS_RAW -->
  <arg name="remove_ground" default="true" />
  <arg name="downsample_cloud" default="false" /> <!-- Apply VoxelGrid Filter with the value given by "leaf_size"-->
  <arg name="leaf_size" default="0.1" /><!-- Voxel Grid Filter leaf size-->
  <arg name="cluster_size_min" default="20" /><!-- Minimum number of points to consider a cluster as valid-->
  <arg name="cluster_size_max" default="100000" /><!-- Maximum number of points to allow inside a cluster-->
  <arg name="sync" default="false" />
  <arg name="use_diffnormals" default="false" />
  <arg name="pose_estimation" default="true" />
  <arg name="clip_min_height" default="-1.3" />
  <arg name="clip_max_height" default="0.5" />

  <arg name="keep_lanes" default="false" />
  <arg name="keep_lane_left_distance" default="5" />
  <arg name="keep_lane_right_distance" default="5" />
  <arg name="cluster_merge_threshold" default="1.5" />
  <arg name="clustering_distance" default="0.75" />

  <arg name="use_vector_map" default="false" />
  <arg name="wayarea_gridmap_layer" default="wayarea" />

  <arg name="output_frame" default="velodyne" />

  <arg name="remove_points_upto" default="0.0" />

  <arg name="use_gpu" default="false" />

  <arg name="use_multiple_thres" default="false"/>
  <arg name="clustering_ranges" default="[15,30,45,60]"/><!-- Distances to segment pointcloud -->
  <arg name="clustering_distances"
       default="[0.5,1.1,1.6,2.1,2.6]"/><!-- Euclidean Clustering threshold distance for each segment -->

  <node pkg="lidar_euclidean_cluster_detect" type="lidar_euclidean_cluster_detect"
        name="lidar_euclidean_cluster_detect" output="screen">
    <param name="points_node"
           value="$(arg points_node)"/> <!-- Can be used to select which pointcloud node will be used as input for the clustering -->
    <param name="remove_ground" value="$(arg remove_ground)"/>
    <param name="downsample_cloud" value="$(arg downsample_cloud)"/>
    <param name="leaf_size" value="$(arg leaf_size)"/>
    <param name="cluster_size_min" value="$(arg cluster_size_min)"/>
    <param name="cluster_size_max" value="$(arg cluster_size_max)"/>
    <param name="use_diffnormals" value="$(arg use_diffnormals)"/>
    <param name="pose_estimation" value="$(arg pose_estimation)"/>
    <param name="keep_lanes" value="$(arg keep_lanes)"/>
    <param name="keep_lane_left_distance" value="$(arg keep_lane_left_distance)"/>
    <param name="keep_lane_right_distance" value="$(arg keep_lane_right_distance)"/>
    <param name="clip_min_height" value="$(arg clip_min_height)"/>
    <param name="clip_max_height" value="$(arg clip_max_height)"/>
    <param name="output_frame" value="$(arg output_frame)"/>
    <param name="remove_points_upto" value="$(arg remove_points_upto)"/>
    <param name="clustering_distance" value="$(arg clustering_distance)"/>
    <param name="cluster_merge_threshold" value="$(arg cluster_merge_threshold)"/>
    <param name="use_gpu" value="$(arg use_gpu)"/>
    <param name="use_multiple_thres" value="$(arg use_multiple_thres)"/>
    <param name="clustering_ranges" value="$(arg clustering_ranges)"/><!-- Distances to segment pointcloud -->
    <param name="clustering_distances"
           value="$(arg clustering_distances)"/><!-- Euclidean Clustering threshold distance for each segment -->

    <remap from="/points_raw" to="/sync_drivers/points_raw" if="$(arg sync)" />
  </node>

  <group if="$(arg use_vector_map)">
    <node name="object_roi_filter_clustering" pkg="roi_object_filter" type="roi_object_filter"
          output="screen" ns="/detection/lidar_detector">
      <param name="objects_src_topic" value="/objects"/>
      <param name="wayarea_gridmap_layer" value="$(arg wayarea_gridmap_layer)"/>
      <param name="sync_topics" value="false"/>
      <param name="exception_list" value="[person, bicycle]"/>
    </node>
    <node pkg="detected_objects_visualizer" type="visualize_detected_objects" name="cluster_detect_visualization_01"
          output="screen" ns="/detection/lidar_detector">
      <param name="objects_src_topic" value="/objects_filtered"/>
    </node>
  </group>
  <group unless="$(arg use_vector_map)">
    <node pkg="detected_objects_visualizer" type="visualize_detected_objects" name="cluster_detect_visualization_01"
          output="screen" ns="/detection/lidar_detector">
      <param name="objects_src_topic" value="/objects"/>
    </node>
  </group>

</launch>
