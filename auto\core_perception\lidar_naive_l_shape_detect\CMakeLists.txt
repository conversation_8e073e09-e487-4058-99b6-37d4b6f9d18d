cmake_minimum_required(VERSION 3.0.2)
project(lidar_naive_l_shape_detect)

find_package(build_flags REQUIRED)

find_package(catkin REQUIRED COMPONENTS
  auto_msgs
  pcl_ros
  roscpp
  tf
)

find_package(OpenCV REQUIRED)
include_directories(/usr/local/include/opencv4)

set(CMAKE_CXX_FLAGS "-O2 -Wall ${CMAKE_CXX_FLAGS}")

catkin_package(
  CATKIN_DEPENDS
    pcl_ros
    auto_msgs
)


link_directories(${OpenCV_LIBRARY_DIRS})

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
)

set(SOURCE_FILES
  nodes/lidar_naive_l_shape_detect/lidar_naive_l_shape_detect_node.cpp
  nodes/lidar_naive_l_shape_detect/lidar_naive_l_shape_detect.cpp
)

add_executable(lidar_naive_l_shape_detect ${SOURCE_FILES})

add_dependencies(lidar_naive_l_shape_detect
  ${catkin_EXPORTED_TARGETS}
)

target_link_libraries(lidar_naive_l_shape_detect
  ${catkin_LIBRARIES}
  ${OpenCV_LIBRARIES}
)

install(
  TARGETS lidar_naive_l_shape_detect
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(
  DIRECTORY launch/
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}/launch
  PATTERN ".svn" EXCLUDE
)
