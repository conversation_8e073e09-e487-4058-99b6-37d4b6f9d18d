Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded: ~
      Splitter Ratio: 0.7357512712478638
    Tree Height: 767
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Name: Time
    SyncMode: 0
    SyncSource: Points Raw
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: false
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: false
    - Class: rviz/Group
      Displays:
        - Class: rviz/TF
          Enabled: true
          Filter (blacklist): ""
          Filter (whitelist): ""
          Frame Timeout: 15
          Frames:
            All Enabled: true
            base_link:
              Value: true
            map:
              Value: true
            velodyne:
              Value: true
            world:
              Value: true
          Marker Alpha: 1
          Marker Scale: 5
          Name: TF
          Show Arrows: true
          Show Axes: true
          Show Names: true
          Tree:
            world:
              map:
                base_link:
                  velodyne:
                    {}
          Update Interval: 0
          Value: true
        - Class: rviz/Group
          Displays:
            - Buffer length: 100
              Class: jsk_rviz_plugin/Plotter2D
              Enabled: true
              Name: Velocity (km/h)
              Show Value: true
              Topic: /linear_velocity_viz
              Value: true
              auto color change: false
              auto scale: true
              auto text size in plot: true
              background color: 0; 0; 0
              backround alpha: 0
              border: true
              foreground alpha: 1
              foreground color: 0; 255; 255
              height: 80
              left: 40
              linewidth: 1
              max color: 255; 0; 0
              max value: 1
              min value: -1
              show caption: true
              text size: 8
              text size in plot: 12
              top: 30
              update interval: 0.03999999910593033
              width: 80
            - Buffer length: 100
              Class: jsk_rviz_plugin/Plotter2D
              Enabled: true
              Name: NDT Time [ms]
              Show Value: true
              Topic: /time_ndt_matching
              Value: true
              auto color change: false
              auto scale: true
              auto text size in plot: true
              background color: 0; 0; 0
              backround alpha: 0
              border: true
              foreground alpha: 1
              foreground color: 0; 255; 255
              height: 80
              left: 140
              linewidth: 1
              max color: 255; 0; 0
              max value: 1
              min value: -1
              show caption: true
              text size: 8
              text size in plot: 12
              top: 30
              update interval: 0.03999999910593033
              width: 80
            - Align Bottom: false
              Background Alpha: 0.8999999761581421
              Background Color: 0; 0; 0
              Class: jsk_rviz_plugin/OverlayText
              Enabled: true
              Foreground Alpha: 0.800000011920929
              Foreground Color: 0; 255; 255
              Invert Shadow: false
              Name: NDT Monitor
              Overtake BG Color Properties: false
              Overtake FG Color Properties: false
              Overtake Position Properties: true
              Topic: /ndt_monitor/ndt_info_text
              Value: true
              font: DejaVu Sans Mono
              height: 50
              left: 40
              line width: 2
              text size: 8
              top: 150
              width: 200
            - Align Bottom: false
              Background Alpha: 0.8999999761581421
              Background Color: 0; 0; 0
              Class: jsk_rviz_plugin/OverlayText
              Enabled: true
              Foreground Alpha: 0.800000011920929
              Foreground Color: 0; 255; 255
              Invert Shadow: false
              Name: Decision Maker Panel
              Overtake BG Color Properties: false
              Overtake FG Color Properties: false
              Overtake Position Properties: true
              Topic: /decision_maker/state_overlay
              Value: true
              font: DejaVu Sans Mono
              height: 200
              left: 40
              line width: 2
              text size: 8
              top: 220
              width: 200
          Enabled: true
          Name: Monitor
        - Class: rviz/Group
          Displays:
            - Align Bottom: false
              Background Alpha: 0.800000011920929
              Background Color: 0; 0; 0
              Class: jsk_rviz_plugin/OverlayText
              Enabled: true
              Foreground Alpha: 0.800000011920929
              Foreground Color: 25; 255; 240
              Invert Shadow: false
              Name: OK
              Overtake BG Color Properties: false
              Overtake FG Color Properties: false
              Overtake Position Properties: true
              Topic: /health_aggregator/ok_text
              Value: true
              font: DejaVu Sans Mono
              height: 80
              left: 40
              line width: 2
              text size: 6
              top: 430
              width: 200
            - Align Bottom: false
              Background Alpha: 0.800000011920929
              Background Color: 0; 0; 0
              Class: jsk_rviz_plugin/OverlayText
              Enabled: true
              Foreground Alpha: 0.800000011920929
              Foreground Color: 255; 255; 0
              Invert Shadow: false
              Name: WARN
              Overtake BG Color Properties: false
              Overtake FG Color Properties: false
              Overtake Position Properties: true
              Topic: /health_aggregator/warn_text
              Value: true
              font: DejaVu Sans Mono
              height: 80
              left: 40
              line width: 2
              text size: 6
              top: 520
              width: 200
            - Align Bottom: false
              Background Alpha: 0.800000011920929
              Background Color: 0; 0; 0
              Class: jsk_rviz_plugin/OverlayText
              Enabled: true
              Foreground Alpha: 0.800000011920929
              Foreground Color: 255; 85; 0
              Invert Shadow: false
              Name: ERROR
              Overtake BG Color Properties: false
              Overtake FG Color Properties: false
              Overtake Position Properties: true
              Topic: /health_aggregator/error_text
              Value: true
              font: DejaVu Sans Mono
              height: 80
              left: 40
              line width: 2
              text size: 6
              top: 620
              width: 200
            - Align Bottom: false
              Background Alpha: 0.800000011920929
              Background Color: 0; 0; 0
              Class: jsk_rviz_plugin/OverlayText
              Enabled: true
              Foreground Alpha: 0.800000011920929
              Foreground Color: 255; 0; 0
              Invert Shadow: false
              Name: FATAL
              Overtake BG Color Properties: false
              Overtake FG Color Properties: false
              Overtake Position Properties: true
              Topic: /health_aggregator/fatal_text
              Value: true
              font: DejaVu Sans Mono
              height: 80
              left: 40
              line width: 2
              text size: 6
              top: 720
              width: 200
          Enabled: false
          Name: Health Checker
      Enabled: true
      Name: System
    - Class: rviz/Group
      Displays:
        - Alpha: 0.05000000074505806
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: Points Map
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 2
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /points_map
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /vector_map
          Name: ADAS Map
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /lanelet2_map_viz
          Name: Lanelet2 Map
          Namespaces:
            {}
          Queue Size: 100
          Value: true
      Enabled: true
      Name: Map
    - Class: rviz/Group
      Displays:
        - Alpha: 0.30000001192092896
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 23.83440399169922
            Min Value: -22.532455444335938
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: Intensity
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: Points Raw
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 2
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /points_raw
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Alpha: 0.5
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 23.83440399169922
            Min Value: -22.532455444335938
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 0; 0
          Color Transformer: FlatColor
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: Points No Ground
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 2
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /points_no_ground
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
      Enabled: true
      Name: Sensing
    - Class: rviz/Group
      Displays:
        - Alpha: 1
          Axes Length: 1
          Axes Radius: 0.10000000149011612
          Class: rviz/Pose
          Color: 255; 170; 255
          Enabled: false
          Head Length: 2
          Head Radius: 2
          Name: Current Pose
          Queue Size: 10
          Shaft Length: 2
          Shaft Radius: 1
          Shape: Arrow
          Topic: /current_pose
          Unreliable: false
          Value: false
        - Alpha: 1
          Axes Length: 1
          Axes Radius: 0.10000000149011612
          Class: rviz/Pose
          Color: 255; 255; 0
          Enabled: false
          Head Length: 2
          Head Radius: 1
          Name: EKF Pose
          Queue Size: 10
          Shaft Length: 2
          Shaft Radius: 0.5
          Shape: Arrow
          Topic: /ekf_pose
          Unreliable: false
          Value: false
        - Alpha: 1
          Axes Length: 1
          Axes Radius: 0.10000000149011612
          Class: rviz/Pose
          Color: 0; 255; 255
          Enabled: false
          Head Length: 2
          Head Radius: 1
          Name: NDT Pose
          Queue Size: 10
          Shaft Length: 2
          Shaft Radius: 0.5
          Shape: Arrow
          Topic: /ndt_pose
          Unreliable: false
          Value: false
      Enabled: true
      Name: Localization
    - Class: rviz/Group
      Displays:
        - Alpha: 0.699999988079071
          Class: rviz/Map
          Color Scheme: map
          Draw Behind: true
          Enabled: true
          Name: Occupancy Grid Map
          Topic: /semantics/costmap_generator/occupancy_grid
          Unreliable: false
          Use Timestamp: false
          Value: true
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 23.83440399169922
            Min Value: -22.532455444335938
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: RGB8
          Decay Time: 0
          Enabled: true
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: Clustered Points
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 5
          Size (m): 0.009999999776482582
          Style: Points
          Topic: /points_cluster
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /detection/object_tracker/objects_markers
          Name: Tracked Objects
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /prediction/motion_predictor/path_markers
          Name: Predicted Path
          Namespaces:
            {}
          Queue Size: 100
          Value: true
      Enabled: true
      Name: Perception
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /waypoint_saver_marker
          Name: Saved Waypoints
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /global_waypoints_mark
          Name: Global Waypoints
          Namespaces:
            global_change_flag_lane_1: true
            global_lane_waypoint_orientation_marker_1: true
            global_velocity_lane_1: false
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /local_waypoints_mark
          Name: Local Waypoints
          Namespaces:
            local_path_marker: true
            local_point_marker: true
            local_waypoint_velocity: true
          Queue Size: 100
          Value: true
        - Class: rviz/Group
          Displays:
            - Class: rviz/Marker
              Enabled: true
              Marker Topic: /safety_border
              Name: Safety Box
              Namespaces:
                {}
              Queue Size: 100
              Value: true
            - Class: jsk_rviz_plugin/BoundingBoxArray
              Enabled: true
              Name: Simulated Obstacle
              Queue Size: 10
              Topic: /dp_planner_tracked_boxes
              Unreliable: false
              Value: true
              alpha: 0.800000011920929
              alpha max: 1
              alpha method: flat
              alpha min: 0
              color: 25; 255; 0
              coloring: Value
              line width: 0.009999999776482582
              only edge: false
              show coords: false
              value threshold: 0
            - Class: rviz/MarkerArray
              Enabled: true
              Marker Topic: /AnimateGlobalPlan
              Name: GlobalPathAnimation
              Namespaces:
                {}
              Queue Size: 100
              Value: true
            - Class: rviz/Marker
              Enabled: true
              Marker Topic: /behavior_state
              Name: Behavior State
              Namespaces:
                {}
              Queue Size: 100
              Value: true
            - Class: rviz/MarkerArray
              Enabled: true
              Marker Topic: /detected_polygons
              Name: Tracked Contours
              Namespaces:
                {}
              Queue Size: 100
              Value: true
            - Class: rviz/MarkerArray
              Enabled: true
              Marker Topic: /local_trajectories
              Name: Local Rollouts
              Namespaces:
                {}
              Queue Size: 100
              Value: true
            - Class: rviz/MarkerArray
              Enabled: true
              Marker Topic: /global_waypoints_rviz
              Name: Global Path
              Namespaces:
                global_lane_array_marker: true
                global_lane_waypoint_orientation_marker: true
                global_velocity_lane_1: true
              Queue Size: 100
              Value: true
          Enabled: true
          Name: OP Planner
      Enabled: true
      Name: Planning
    - Class: rviz/Group
      Displays:
        - Class: rviz/Marker
          Enabled: true
          Marker Topic: /trajectory_circle_mark
          Name: Pure Pursuit Trajectory
          Namespaces:
            trajectory_circle_marker: true
          Queue Size: 100
          Value: true
        - Class: rviz/Marker
          Enabled: true
          Marker Topic: /next_target_mark
          Name: Pure Pursuit Target
          Namespaces:
            next_target_marker: true
          Queue Size: 100
          Value: true
        - Class: rviz/Marker
          Enabled: true
          Marker Topic: /mpc_follower/debug/predicted_traj
          Name: MPC Predicted Trajectory
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/Marker
          Enabled: true
          Marker Topic: /mpc_follower/debug/filtered_traj
          Name: MPC Reference Trajectory
          Namespaces:
            {}
          Queue Size: 100
          Value: true
      Enabled: true
      Name: Control
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /vector_map_center_lines_rviz
      Name: MarkerArray
      Namespaces:
        road_network_vector_map: true
      Queue Size: 100
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /obstacle_detection_markers
      Name: MarkerArray
      Namespaces:
        detection_range: true
        height_range: true
        radar_position: true
      Queue Size: 100
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Default Light: true
    Fixed Frame: world
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Angle: 0.005000126548111439
      Class: rviz/TopDownOrtho
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Scale: 39.320926666259766
      Target Frame: base_link
      X: -3.797349691390991
      Y: 0.8845246434211731
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 1016
  Hide Left Dock: false
  Hide Right Dock: true
  QMainWindow State: 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
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 1848
  X: 72
  Y: 27
