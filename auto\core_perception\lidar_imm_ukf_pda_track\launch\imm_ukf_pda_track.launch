<!-- -->
<launch>
  <arg name="namespace" default="/detection/object_tracker"/>
  <arg name="tracker_input_topic" default="/detection/lidar_detector/objects" />
  <arg name="tracker_output_topic" default="/detection/object_tracker/objects" />

  <!-- 全局参考框架，适用于所有传感器数据转换。 -->
  <arg name="tracking_frame" default="base_link" />
  <!-- 门控阈值，定义了观测值与预测值间可接受的最大偏差，较大的值可能导致错误数据关联。 -->
  <arg name="gating_threshold" default="9.22" />
  <!-- 门控概率，几乎确定认为门控范围内的测量属于同一目标，过高可能导致有效测量被排除。 -->
  <arg name="gate_probability" default="0.99" />
  <!-- 检测概率，较高的设置确保目标的可靠检测。 -->
  <arg name="detection_probability" default="0.9" />
  <!-- 生命周期阈值，目标需要在连续多个帧中被检测到才认为是有效的，有助于避免错误检测。 -->
  <arg name="life_time_threshold" default="8" />
  <!-- 静态速度阈值，低于此速度的目标被视为静态，适用于处理停止的车辆或设备。 -->
  <arg name="static_velocity_threshold" default="0.5" />
  <!-- 静态历史阈值，连续多少帧内目标速度低于阈值时才判定为静态。 -->
  <arg name="static_num_history_threshold" default="3" />
  <!-- 防止滤波器发散的阈值，处理高不确定性测量时保持滤波器稳定。 -->
  <arg name="prevent_explosion_threshold" default="1000" />
  <!-- 合并距离阈值，小于此距离的目标可能被视为同一目标，可能需要根据具体场景调整。 -->
  <arg name="merge_distance_threshold" default="1.0" />
  <!-- 是否使用Sigma点UKF算法，影响计算复杂度和跟踪精度的平衡。 -->
  <arg name="use_sukf" default="false" />

  <!-- 以下参数用于矢量地图辅助的跟踪 -->
  <!-- 是否使用矢量地图辅助追踪，增强追踪精度。 -->
  <arg name="use_vectormap" default="false" />
  <!-- 车道方向卡方阈值，用于确定车道方向的一致性。 -->
  <arg name="lane_direction_chi_threshold" default="2.71" />
  <!-- 最近车道距离阈值，定义车辆与最近车道的最大允许距离。 -->
  <arg name="nearest_lane_distance_threshold" default="1.0" />
  <!-- 矢量地图坐标系，所有矢量地图数据都将转换到该坐标系中。 -->
  <arg name="vectormap_frame" default="map" />

  <!-- 用于基准测试的参数 -->
  <!-- 是否进行基准测试，开启后将记录相关测试数据。 -->
  <arg name="is_benchmark" default="false" />
  <!-- KITTI数据目录，存放基准测试数据的路径。 -->
  <arg name="kitti_data_dir" default="" />

  <!-- rosrun lidar_tracker euclidean_cluster _points_node:="" -->
  <node pkg="imm_ukf_pda_track" type="imm_ukf_pda" name="imm_ukf_pda_01" output="screen">
    <param name="gating_threshold" value="$(arg gating_threshold)" />
    <param name="gate_probability" value="$(arg gate_probability)" />
    <param name="detection_probability" value="$(arg detection_probability)" />
    <param name="life_time_threshold" value="$(arg life_time_threshold)" />
    <param name="static_velocity_threshold" value="$(arg static_velocity_threshold)" />
    <param name="static_num_history_threshold" value="$(arg static_num_history_threshold)" />
    <param name="prevent_explosion_threshold" value="$(arg prevent_explosion_threshold)" />
    <param name="lane_direction_chi_threshold" value="$(arg lane_direction_chi_threshold)" />
    <param name="nearest_lane_distance_threshold" value="$(arg nearest_lane_distance_threshold)" />
    <param name="tracking_frame" value="$(arg tracking_frame)" />
    <param name="vectormap_frame" value="$(arg vectormap_frame)" />
    <param name="use_sukf" value="$(arg use_sukf)" />
    <param name="use_vectormap" value="$(arg use_map_info)" />
    <param name="merge_distance_threshold" value="$(arg merge_distance_threshold)" />

    <remap from="detection/fusion_tools/objects" to="$(arg tracker_input_topic)" />
    <remap from="detection/objects" to="$(arg tracker_output_topic)" />
  </node>

  <node pkg="detected_objects_visualizer" type="visualize_detected_objects" name="ukf_track_visualization_01" output="screen" ns="$(arg namespace)"/>

  <node pkg="topic_tools" type="relay" name="ukf_track_relay_01" output="screen" args="$(arg namespace)/objects /detection/objects"/>

</launch>
