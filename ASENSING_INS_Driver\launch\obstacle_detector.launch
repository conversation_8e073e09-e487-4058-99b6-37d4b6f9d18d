<?xml version="1.0"?>
<launch>
    <!-- 障碍物检测器启动文件 -->
    
    <node name="obstacle_detector" pkg="obstacle_detector" type="obstacle_detector_node" output="screen">
        <!-- 矩形检测参数 -->
        <param name="point_threshold" value="10" />        <!-- 点云数量阈值 -->
        <param name="detection_length" value="7.0" />      <!-- 基础检测长度(米) - 前后方向 -->
        <param name="detection_width" value="1.5" />       <!-- 基础检测宽度(米) - 左右方向 -->

        <!-- 动态安全框参数 -->
        <param name="steering_angle_threshold" value="0.1" />  <!-- 转角阈值(弧度)，约5.7度 -->
        <param name="safety_box_scale_factor" value="0.5" />   <!-- 转弯时安全框缩放因子 -->
        <param name="detection_front_ratio" value="1" />     <!-- 前向检测比例: 1.0=全部, 0.5=前半部分, 0.33=前1/3 -->
        
        <!-- 雷达位置 -->
        <param name="radar_x" value="0.0" />               <!-- 雷达X坐标 -->
        <param name="radar_y" value="0.0" />               <!-- 雷达Y坐标 -->
        <param name="radar_z" value="0.0" />               <!-- 雷达Z坐标 -->
        
        <!-- 高度过滤 -->
        <param name="min_height" value="-1.0" />           <!-- 最小高度 -->
        <param name="max_height" value="1.5" />            <!-- 最大高度 -->
        
        <!-- 可视化参数 -->
        <param name="frame_id" value="base_link" />        <!-- 坐标系 -->
        <param name="enable_visualization" value="true" /> <!-- 启用可视化 -->
        
        <!-- 输入话题选择 -->
        <!-- 选项1: /points_no_ground (推荐) - 已过滤地面的完整点云 -->
        <!-- 选项2: /points_cluster - 已聚类的障碍物点云 -->
        <param name="input_topic" value="/points_no_ground" /><!-- 输入点云话题 -->
        
        <!-- 警告间隔 -->
        <param name="warning_interval" value="0.1" />      <!-- 警告间隔时间(秒) -->
    </node>
    
</launch>
