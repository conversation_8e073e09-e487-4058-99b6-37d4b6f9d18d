/*
 * Copyright 2019  Foundation. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 *
 * v1.0 Ma<PERSON><PERSON>
 */

#ifndef health_checker_HEALTH_CHECKER_DIAG_BUFFER_H
#define health_checker_HEALTH_CHECKER_DIAG_BUFFER_H
// headers in Autoare
#include <system_health_checker/constants.h>
#include <system_msgs/DiagnosticStatusArray.h>

// headers in STL
#include <map>
#include <mutex>
#include <string>
#include <vector>

// headers in ROS
#include <ros/ros.h>

namespace health_checker
{
class DiagBuffer
{
public:
  DiagBuffer(<PERSON>rro<PERSON><PERSON><PERSON> key, ErrorType type, std::string description,
             double buffer_duration);
  void addDiag(system_msgs::DiagnosticStatus status);
  system_msgs::DiagnosticStatusArray getAndClearData();
  const ErrorType type;
  const std::string description;

private:
  using AwDiagStatus = system_msgs::DiagnosticStatus;
  std::mutex mtx_;
  ErrorLevel getErrorLevel();
  void updateBuffer();
  ErrorKey key_;
  ros::Duration buffer_duration_;
  std::map<ErrorLevel, system_msgs::DiagnosticStatusArray> buffer_;
  system_msgs::DiagnosticStatusArray filterBuffer(
    ros::Time now, ErrorLevel level);
  ros::Publisher status_pub_;
  bool isOlderTimestamp(const system_msgs::DiagnosticStatus &a,
                        const system_msgs::DiagnosticStatus &b);
};
}  // namespace health_checker

#endif  // health_checker_HEALTH_CHECKER_DIAG_BUFFER_H
