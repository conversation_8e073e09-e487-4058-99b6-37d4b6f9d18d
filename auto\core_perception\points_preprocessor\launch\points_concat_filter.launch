<!-- -->
<launch>
  <arg name="input_topics" default="[/points_alpha, /points_beta]" />
  <arg name="output_topic" default="/points_concat" />
  <arg name="output_frame_id" default="velodyne" />

  <node pkg="points_preprocessor" type="points_concat_filter"
        name="points_concat_filter" output="screen">
    <param name="output_frame_id" value="$(arg output_frame_id)" />
    <param name="input_topics" value="$(arg input_topics)" />
    <remap from="/points_concat" to="$(arg output_topic)" />
  </node>
</launch>
