{"ros.distro": "noetic", "python.autoComplete.extraPaths": ["/opt/ros/noetic/lib/python3/dist-packages"], "python.analysis.extraPaths": ["/opt/ros/noetic/lib/python3/dist-packages"], "files.associations": {"cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "hash_map": "cpp", "hash_set": "cpp", "strstream": "cpp", "bit": "cpp", "bitset": "cpp", "cfenv": "cpp", "charconv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "cstdint": "cpp", "cuchar": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "netfwd": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "string": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "rope": "cpp", "slist": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "ranges": "cpp", "scoped_allocator": "cpp", "shared_mutex": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp"}, "cmake.sourceDirectory": "/home/<USER>/auto_djyx-2.2.0/src/slam/hdl_graph_slam"}