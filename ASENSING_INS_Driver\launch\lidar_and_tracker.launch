<?xml version="1.0"?>
<launch>
  <!-- lidar_euclidean_cluster_detect -->
  <!-- points_node: 指定从哪个节点接收点云数据，默认为"/points_no_ground"。根据实际需要，可以修改为从VSCAN或POINTS_RAW读取数据。 -->
  <arg name="points_node" default="/points_no_ground" />

  <!-- remove_ground: 是否在数据处理前移除地面点，用以减少处理数据量，提高系统性能。 -->
  <arg name="remove_ground" default="false" />

  <!-- downsample_cloud: 是否应用下采样来减少点云数据的密度，此操作使用VoxelGrid滤波器，通过leaf_size设置其粒度。 -->
  <arg name="downsample_cloud" default="false" />

  <!-- leaf_size: 设置VoxelGrid滤波器的体素大小，控制下采样的精细程度。较小的体素尺寸可以维持更多细节，但增加计算量。 -->
  <arg name="leaf_size" default="0.1" />

  <!-- cluster_size_min: 设置一个聚类有效的最小点数，默认为3，可以根据对象大小进行调整。 -->
  <arg name="cluster_size_min" default="5" />

  <!-- cluster_size_max: 设置一个聚类中允许的最大点数，用于限制聚类的大小。 -->
  <arg name="cluster_size_max" default="10000" />

  <!-- sync: 是否同步输入的点云数据，通常用于时间同步处理。 -->
  <arg name="sync" default="false" />

  <!-- use_diffnormals: 是否使用差分法估算法线来帮助分割点云中的不同对象，通常用于复杂环境中。 -->
  <arg name="use_diffnormals" default="false" />

  <!-- pose_estimation: 是否进行姿态估计，通常用于需要定位检测到对象的具体方向和姿态的应用。 -->
  <arg name="pose_estimation" default="false" />

  <!-- clip_min_height 和 clip_max_height: 设置检测对象的高度范围，此参数与地面的相对高度密切相关。 -->
  <arg name="clip_min_height" default="-1.0" />
  <arg name="clip_max_height" default="1.4" />

  <!-- keep_lanes: 是否在车道内保持检测，用于限制检测范围，减少误检。 -->
  <arg name="keep_lanes" default="false" />

  <!-- keep_lane_left_distance 和 keep_lane_right_distance: 设置车道保持检测的左右距离限制。 -->
  <arg name="keep_lane_left_distance" default="2" />
  <arg name="keep_lane_right_distance" default="2" />

  <!-- cluster_merge_threshold: 设置聚类合并的距离阈值，较小的值可以减少对象间的合并，保持更高的分辨率。 -->
  <arg name="cluster_merge_threshold" default="0.6" />

  <!-- clustering_distance: 欧几里得聚类使用的距离阈值，用于决定点云中哪些点属于同一个对象。 -->
  <arg name="clustering_distance" default="0.3" />

  <!-- use_vector_map: 是否使用向量地图来过滤检测结果，常用于需要地理信息增强的应用。 -->
  <arg name="use_vector_map" default="false" />

  <!-- wayarea_gridmap_layer: 设置使用的网格地图层的名称，与向量地图相关。 -->
  <arg name="wayarea_gridmap_layer" default="wayarea" />

  <!-- output_frame: 输出数据的参考框架，默认为地图坐标系，用于需要在全局坐标系中使用检测结果的场合。 -->
  <arg name="output_frame" default="map" />
  <!-- 默认障碍物在velodyne坐标系下，而lidar_kf_contour_track需要地图坐标系下的障碍物信息，所以修改为map-->

  <!-- remove_points_upto: 设置移除点云数据直到某个距离的参数，用于过滤近距离的噪声或无关数据。 -->
  <arg name="remove_points_upto" default="0.0" />

  <!-- use_gpu: 是否使用GPU加速处理。 -->
  <arg name="use_gpu" default="true" />

  <!-- use_multiple_thres: 是否使用多阈值分段处理。 -->
  <arg name="use_multiple_thres" default="true" />

  <!-- clustering_ranges: 点云分段的距离范围。 -->
  <arg name="clustering_ranges" default="[12,25,40,60]" />
  <!-- clustering_distances: 每个分段的欧几里得聚类阈值。 -->
  <arg name="clustering_distances" default="[0.4,0.8,1.2,1.8]" />
  <!-- Euclidean Clustering threshold distance for each segment -->

  <node pkg="lidar_euclidean_cluster_detect" type="lidar_euclidean_cluster_detect"
    name="lidar_euclidean_cluster_detect" output="screen" respawn="true">
    <param name="points_node" value="$(arg points_node)" />
    <!-- Can be used to select which pointcloud node will be used as input for the clustering -->
    <param name="remove_ground" value="$(arg remove_ground)" />
    <param name="downsample_cloud" value="$(arg downsample_cloud)" />
    <param name="leaf_size" value="$(arg leaf_size)" />
    <param name="cluster_size_min" value="$(arg cluster_size_min)" />
    <param name="cluster_size_max" value="$(arg cluster_size_max)" />
    <param name="use_diffnormals" value="$(arg use_diffnormals)" />
    <param name="pose_estimation" value="$(arg pose_estimation)" />
    <param name="keep_lanes" value="$(arg keep_lanes)" />
    <param name="keep_lane_left_distance" value="$(arg keep_lane_left_distance)" />
    <param name="keep_lane_right_distance" value="$(arg keep_lane_right_distance)" />
    <param name="clip_min_height" value="$(arg clip_min_height)" />
    <param name="clip_max_height" value="$(arg clip_max_height)" />
    <param name="output_frame" value="$(arg output_frame)" />
    <param name="remove_points_upto" value="$(arg remove_points_upto)" />
    <param name="clustering_distance" value="$(arg clustering_distance)" />
    <param name="cluster_merge_threshold" value="$(arg cluster_merge_threshold)" />
    <param name="use_gpu" value="$(arg use_gpu)" />
    <param name="use_multiple_thres" value="$(arg use_multiple_thres)" />
    <param name="clustering_ranges" value="$(arg clustering_ranges)" />
    <!-- Distances to segment pointcloud -->
    <param name="clustering_distances" value="$(arg clustering_distances)" />
    <!-- Euclidean Clustering threshold distance for each segment -->

    <remap from="/points_raw" to="/sync_drivers/points_raw" if="$(arg sync)" />
  </node>

  <group if="$(arg use_vector_map)">
    <node name="object_roi_filter_clustering" pkg="roi_object_filter" type="roi_object_filter"
      output="screen" ns="/detection/lidar_detector">
      <param name="objects_src_topic" value="/objects" />
      <param name="wayarea_gridmap_layer" value="$(arg wayarea_gridmap_layer)" />
      <param name="sync_topics" value="false" />
      <param name="exception_list" value="[person, bicycle]" />
    </node>
    <node pkg="detected_objects_visualizer" type="visualize_detected_objects"
      name="cluster_detect_visualization_01" output="screen" ns="/detection/lidar_detector">
      <param name="objects_src_topic" value="/objects_filtered" />
    </node>
  </group>
  <group unless="$(arg use_vector_map)">
    <node pkg="detected_objects_visualizer" type="visualize_detected_objects"
      name="cluster_detect_visualization_01" output="screen" ns="/detection/lidar_detector">
      <param name="objects_src_topic" value="/objects" />
    </node>
  </group>

  <!-- lidar_kf_contour_track -->
  <!-- vehicle_width: 车辆的宽度，单位为米。增加此值将允许系统识别更宽的车辆，默认为1.2米。 -->
  <arg name="vehicle_width" default="1.2" />

  <!-- vehicle_length: 车辆的长度，单位为米。增加此值可以让系统处理更长的车辆，默认为1.85米。 -->
  <arg name="vehicle_length" default="1.85" />

  <!-- min_object_size: 可检测的最小对象大小，单位为米。减小此值将使系统能够检测到更小的对象，默认为0.1米。 -->
  <arg name="min_object_size" default="0.1" />

  <!-- max_object_size: 可检测的最大对象大小，单位为米。增加此值让系统能够识别更大的对象，默认为15.0米。 -->
  <arg name="max_object_size" default="15.0" />

  <!-- polygon_quarters: 多边形细分的段数。增加此数值将提高轮廓的精确度，默认为16段。 -->
  <arg name="polygon_quarters" default="16" />

  <!-- polygon_resolution: 多边形的分辨率，单位为米。减小此值可以提高边界的精度，默认为0.5米。 -->
  <arg name="polygon_resolution" default="0.5" />

  <!-- tracking_type: 对象追踪的类型，可选0、1或2。0代表仅关联，1代表简单的卡尔曼滤波追踪，2代表智能轮廓追踪。选择不同的追踪类型会影响追踪的效果和性能，默认为1。 -->
  <arg name="tracking_type" default="1" />

  <!-- max_association_distance: 对象关联的最大距离，单位为米。增加此值可以在更远的距离关联检测到的对象，默认为0.5米。 -->
  <arg name="max_association_distance" default="0.5" />

  <!-- max_association_size_diff: 对象关联时的最大大小偏差，单位为米。增加此值允许系统在确定对象关联时接受更大的大小变化，默认为0.1米。 -->
  <arg name="max_association_size_diff" default="0.1" />

  <!-- max_remeber_time: 对象丢失后的最大记忆时间，单位为秒。增加此值将让系统在更长时间内记住丢失的对象，有助于在对象重新出现时快速重新关联，默认为10秒。 -->
  <arg name="max_remeber_time" default="10" />

  <!-- trust_counter: 追踪对象的信任计数器，计数达到此值时确认对象。增加此值可以减少误判，但可能降低追踪的灵敏度，默认为2次。 -->
  <arg name="trust_counter" default="2" />

  <!-- enableSimulationMode: 是否启用仿真模式，布尔值。启用此模式可以测试系统性能而不接入实时数据，默认为false。 -->
  <arg name="enableSimulationMode" default="false" />

  <!-- enableStepByStepMode: 是否启用逐步模式，布尔值。启用此模式允许单步执行系统操作，便于调试和教学，默认为false。 -->
  <arg name="enableStepByStepMode" default="false" />

  <!-- vector_map_filter_distance: 矢量地图过滤的距离，单位为米。设为非零值可以启用矢量地图过滤，有助于提高定位精度，默认为0米（禁用）。 -->
  <arg name="vector_map_filter_distance" default="0" />

  <!-- enableLogging: 是否启用日志记录，布尔值。启用此选项可以记录系统操作和事件，有助于后期分析和故障排查，默认为false。 -->
  <arg name="enableLogging" default="false" />

  <node pkg="lidar_kf_contour_track" type="lidar_kf_contour_track" name="lidar_kf_contour_track"
    output="screen" respawn="true">

    <param name="vehicle_width" value="$(arg vehicle_width)" />
    <param name="vehicle_length" value="$(arg vehicle_length)" />
    <param name="min_object_size" value="$(arg min_object_size)" />
    <param name="max_object_size" value="$(arg max_object_size)" />
    <param name="polygon_quarters" value="$(arg polygon_quarters)" />
    <param name="polygon_resolution" value="$(arg polygon_resolution)" />
    <param name="tracking_type" value="$(arg tracking_type)" />
    <param name="max_association_distance" value="$(arg max_association_distance)" />
    <param name="max_association_size_diff" value="$(arg max_association_size_diff)" />

    <param name="max_remeber_time" value="$(arg max_remeber_time)" />
    <param name="trust_counter" value="$(arg trust_counter)" />

    <param name="enableSimulationMode" value="$(arg enableSimulationMode)" />
    <param name="enableStepByStepMode" value="$(arg enableStepByStepMode)" />

    <param name="vector_map_filter_distance" value="$(arg vector_map_filter_distance)" />
    <param name="enableLogging" value="$(arg enableLogging)" />
  </node>

</launch>