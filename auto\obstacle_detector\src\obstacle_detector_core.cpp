#include "obstacle_detector/obstacle_detector_core.h"
#include <pcl/common/distances.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <sensor_msgs/point_cloud2_iterator.h>
#include <sstream>
#include <iomanip>

namespace obstacle_detector
{

ObstacleDetectorCore::ObstacleDetectorCore(ros::NodeHandle& nh, ros::NodeHandle& private_nh)
    : nh_(nh)
    , private_nh_(private_nh)
    , filtered_cloud_(new pcl::PointCloud<pcl::PointXYZ>)
    , warning_interval_(2.0)
    , current_steering_angle_(0.0)
    , is_reverse_gear_(false)
    , current_gear_(0x22)  // 默认N档
{
    // TF功能已移除，不再需要坐标变换
}

ObstacleDetectorCore::~ObstacleDetectorCore()
{
}

bool ObstacleDetectorCore::initialize()
{
    // 加载参数
    loadParameters();

    // 初始化发布器
    marker_pub_ = nh_.advertise<visualization_msgs::MarkerArray>("/obstacle_detection_markers", 10);
    filtered_points_pub_ = nh_.advertise<sensor_msgs::PointCloud2>("/filtered_points", 10);
    obstacle_detected_pub_ = nh_.advertise<std_msgs::Bool>("/obstacle_detected", 10);

    // 初始化订阅器
    pointcloud_sub_ = nh_.subscribe(input_topic_, 10,
                                   &ObstacleDetectorCore::pointCloudCallback, this);
    ctrl_cmd_sub_ = nh_.subscribe("/ctrl_cmd", 10,
                                 &ObstacleDetectorCore::controlCommandCallback, this);
    vehicle_state_sub_ = nh_.subscribe("/vehicle_gear", 10,
                                      &ObstacleDetectorCore::vehicleStateCallback, this);

    // 初始化可视化定时器
    if (enable_visualization_)
    {
        visualization_timer_ = nh_.createTimer(ros::Duration(0.1), 
                                             &ObstacleDetectorCore::publishVisualization, this);
    }

    // 初始化时间
    last_warning_time_ = std::chrono::steady_clock::now();

    ROS_INFO("Obstacle detector started");
    ROS_INFO("Normal mode detection params: rectangular %.2fx%.2fm, threshold=%d points",
             detection_length_, detection_width_, point_threshold_);
    ROS_INFO("Reverse mode detection params: rectangular %.2fx%.2fm",
             reverse_detection_length_, reverse_detection_width_);
    ROS_INFO("Radar position: (%.2f, %.2f, %.2f)", radar_x_, radar_y_, radar_z_);
    ROS_INFO("Frame: %s, Visualization: %s", frame_id_.c_str(),
             enable_visualization_ ? "enabled" : "disabled");

    return true;
}

void ObstacleDetectorCore::loadParameters()
{
    private_nh_.param("point_threshold", point_threshold_, 50);
    private_nh_.param("radar_x", radar_x_, 0.0);
    private_nh_.param("radar_y", radar_y_, 0.0);
    private_nh_.param("radar_z", radar_z_, 0.0);
    private_nh_.param("min_height", min_height_, -2.0);
    private_nh_.param("max_height", max_height_, 3.0);
    private_nh_.param("frame_id", frame_id_, std::string("base_link"));
    private_nh_.param("enable_visualization", enable_visualization_, true);
    private_nh_.param("input_topic", input_topic_, std::string("/points_no_ground"));
    private_nh_.param("warning_interval", warning_interval_, 2.0);

    // 正常状态矩形检测参数
    private_nh_.param("detection_length", base_detection_length_, 3.0);  // 默认3米长
    private_nh_.param("detection_width", base_detection_width_, 2.0);    // 默认2米宽

    // 正常状态动态安全框参数
    private_nh_.param("steering_angle_threshold", steering_angle_threshold_, 0.1);  // 转角阈值(弧度)，约5.7度
    private_nh_.param("safety_box_scale_factor", safety_box_scale_factor_, 0.7);    // 缩放因子，转弯时缩小到70%
    private_nh_.param("detection_front_ratio", detection_front_ratio_, 1.0);        // 前向检测比例，1.0=全部，0.5=前半部分，0.33=前1/3

    // 倒车状态安全框参数
    private_nh_.param("reverse_detection_length", reverse_detection_length_, 2.0);  // 倒车检测长度，默认2米
    private_nh_.param("reverse_detection_width", reverse_detection_width_, 1.5);    // 倒车检测宽度，默认1.5米
    private_nh_.param("reverse_steering_angle_threshold", reverse_steering_angle_threshold_, 0.15);  // 倒车转角阈值
    private_nh_.param("reverse_safety_box_scale_factor", reverse_safety_box_scale_factor_, 0.6);     // 倒车缩放因子
    private_nh_.param("reverse_detection_front_ratio", reverse_detection_front_ratio_, 0.8);        // 倒车前向检测比例

    // 初始化当前检测尺寸为基础尺寸
    detection_length_ = base_detection_length_;
    detection_width_ = base_detection_width_;

    // 添加调试信息
    ROS_DEBUG("Loaded normal mode parameters: base_rectangle=%.2fx%.2f, threshold=%d, warning_interval=%.2f",
              base_detection_length_, base_detection_width_, point_threshold_, warning_interval_);
    ROS_DEBUG("Normal mode dynamic safety box: steering_threshold=%.3f rad, scale_factor=%.2f",
              steering_angle_threshold_, safety_box_scale_factor_);
    ROS_DEBUG("Loaded reverse mode parameters: rectangle=%.2fx%.2f",
              reverse_detection_length_, reverse_detection_width_);
    ROS_DEBUG("Reverse mode dynamic safety box: steering_threshold=%.3f rad, scale_factor=%.2f",
              reverse_steering_angle_threshold_, reverse_safety_box_scale_factor_);
}

void ObstacleDetectorCore::pointCloudCallback(const sensor_msgs::PointCloud2::ConstPtr& cloud_msg)
{
    try
    {
        // 轻量级处理：降低频率，每3帧处理1帧
        static int frame_counter = 0;
        frame_counter++;
        if (frame_counter % 3 != 0)
        {
            return;
        }

        // 检查点云数据有效性
        if (cloud_msg->width == 0 || cloud_msg->height == 0)
        {
            ROS_WARN_THROTTLE(5.0, "Received empty point cloud");
            return;
        }

        // 快速检测：直接在ROS消息中计算，避免PCL转换
        int points_in_range = 0;
        double closest_distance = std::max(detection_length_, detection_width_);

        // 解析点云数据
        sensor_msgs::PointCloud2ConstIterator<float> iter_x(*cloud_msg, "x");
        sensor_msgs::PointCloud2ConstIterator<float> iter_y(*cloud_msg, "y");
        sensor_msgs::PointCloud2ConstIterator<float> iter_z(*cloud_msg, "z");

        for (; iter_x != iter_x.end(); ++iter_x, ++iter_y, ++iter_z)
        {
            float x = *iter_x;
            float y = *iter_y;
            float z = *iter_z;

            // 检查点是否有效
            if (!std::isfinite(x) || !std::isfinite(y) || !std::isfinite(z))
                continue;

            // 高度过滤
            if (z < min_height_ || z > max_height_)
                continue;

            // 计算相对于雷达的位置
            double dx = x - radar_x_;
            double dy = y - radar_y_;
            double dz = z - radar_z_;

            bool in_range = false;
            double distance = sqrt(dx*dx + dy*dy + dz*dz);

            // 矩形检测：检查是否在矩形范围内
            // X轴为前后方向，Y轴为左右方向
            // 应用前向检测比例裁剪 - 保留前方指定比例的区域
            double original_front_boundary = radar_x_ + detection_length_ / 2.0;  // 原始前边界
            double original_rear_boundary = radar_x_ - detection_length_ / 2.0;   // 原始后边界

            // 计算有效检测区域：从雷达位置开始，向前延伸指定比例的距离
            double effective_front_distance = detection_length_ * detection_front_ratio_ / 2.0;
            double effective_front_boundary = radar_x_ + effective_front_distance;  // 有效前边界
            double effective_rear_boundary = radar_x_;  // 从雷达位置开始

            // 检查点是否在裁剪后的检测区域内（只检测雷达前方的指定比例区域）
            if (x >= effective_rear_boundary && x <= effective_front_boundary && abs(dy) <= detection_width_/2.0)
            {
                in_range = true;
            }

            if (in_range)
            {
                points_in_range++;
                if (distance < closest_distance)
                {
                    closest_distance = distance;
                }
            }
        }

        // 添加调试信息
        ROS_DEBUG_THROTTLE(5.0, "Processed frame: detected %d points in %.2fx%.2fm rectangle (threshold: %d)",
                          points_in_range, detection_length_, detection_width_, point_threshold_);

        // 发布障碍物检测状态
        std_msgs::Bool obstacle_msg;
        obstacle_msg.data = (points_in_range > point_threshold_);
        obstacle_detected_pub_.publish(obstacle_msg);

        // 检查是否超过阈值
        if (points_in_range > point_threshold_)
        {
            // 检查警告间隔
            if (checkWarningInterval())
            {
                ROS_WARN("*** OBSTACLE WARNING ***");
                ROS_WARN("Detected %d points (threshold: %d)", points_in_range, point_threshold_);
                ROS_WARN("Closest distance: %.2fm", closest_distance);
                ROS_WARN("Detection area: %.2fx%.2fm rectangle", detection_length_, detection_width_);
            }
        }
        else
        {
            ROS_INFO_THROTTLE(10.0, "Detected %d points in rectangle, below threshold %d", points_in_range, point_threshold_);
        }
    }
    catch (const std::exception& e)
    {
        ROS_ERROR_THROTTLE(5.0, "Error in lightweight point cloud processing: %s", e.what());
    }
}



double ObstacleDetectorCore::calculateDistance(const pcl::PointXYZ& p1, const pcl::PointXYZ& p2)
{
    return std::sqrt(std::pow(p1.x - p2.x, 2) + 
                    std::pow(p1.y - p2.y, 2) + 
                    std::pow(p1.z - p2.z, 2));
}

void ObstacleDetectorCore::triggerWarning(int point_count, const pcl::PointCloud<pcl::PointXYZ>::Ptr& points)
{
    if (!checkWarningInterval())
    {
        return;
    }

    // 计算最近障碍物距离
    double min_distance = std::numeric_limits<double>::max();
    pcl::PointXYZ radar_point(radar_x_, radar_y_, radar_z_);
    
    for (const auto& point : points->points)
    {
        double distance = calculateDistance(point, radar_point);
        if (distance < min_distance)
        {
            min_distance = distance;
        }
    }

    // 分析障碍物分布
    std::string obstacle_info = analyzeObstacleDistribution(points);

    // 发出警告
    std::stringstream warning_msg;
    warning_msg << "*** OBSTACLE WARNING ***\n"
                << "Detected " << point_count << " points (threshold: " << point_threshold_ << ")\n"
                << "Closest distance: " << std::fixed << std::setprecision(2) << min_distance << "m\n"
                << "Detection area: " << detection_length_ << "x" << detection_width_ << "m rectangle\n"
                << "Obstacle distribution: " << obstacle_info;

    ROS_WARN("%s", warning_msg.str().c_str());
}

std::string ObstacleDetectorCore::analyzeObstacleDistribution(const pcl::PointCloud<pcl::PointXYZ>::Ptr& points)
{
    if (points->empty())
    {
        return "No obstacles";
    }

    int close_points = 0;
    int medium_points = 0;
    int far_points = 0;
    
    pcl::PointXYZ radar_point(radar_x_, radar_y_, radar_z_);

    for (const auto& point : points->points)
    {
        double distance = calculateDistance(point, radar_point);
        
        if (distance <= 2.0)
        {
            close_points++;
        }
        else if (distance <= 4.0)
        {
            medium_points++;
        }
        else
        {
            far_points++;
        }
    }

    std::vector<std::string> distribution;
    if (close_points > 0)
    {
        distribution.push_back("close(" + std::to_string(close_points) + "pts)");
    }
    if (medium_points > 0)
    {
        distribution.push_back("medium(" + std::to_string(medium_points) + "pts)");
    }
    if (far_points > 0)
    {
        distribution.push_back("far(" + std::to_string(far_points) + "pts)");
    }

    if (distribution.empty())
    {
        return "Unknown distribution";
    }

    std::string result;
    for (size_t i = 0; i < distribution.size(); ++i)
    {
        if (i > 0) result += ", ";
        result += distribution[i];
    }
    
    return result;
}

bool ObstacleDetectorCore::checkWarningInterval()
{
    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::duration<double>>(
        current_time - last_warning_time_).count();

    if (duration >= warning_interval_)
    {
        last_warning_time_ = current_time;
        return true;
    }

    return false;
}

void ObstacleDetectorCore::publishVisualization(const ros::TimerEvent& event)
{
    if (!enable_visualization_)
    {
        return;
    }

    visualization_msgs::MarkerArray marker_array;

    // 创建矩形检测范围标记
    visualization_msgs::Marker detection_rectangle = createDetectionRectangleMarker();
    marker_array.markers.push_back(detection_rectangle);

    // 创建雷达位置标记
    visualization_msgs::Marker radar_marker = createRadarPositionMarker();
    marker_array.markers.push_back(radar_marker);

    // 创建高度范围标记
    std::vector<visualization_msgs::Marker> height_markers = createHeightRangeMarkers();
    marker_array.markers.insert(marker_array.markers.end(),
                                height_markers.begin(), height_markers.end());

    // 发布标记
    marker_pub_.publish(marker_array);
}



visualization_msgs::Marker ObstacleDetectorCore::createDetectionRectangleMarker()
{
    visualization_msgs::Marker marker;
    marker.header.frame_id = frame_id_;
    marker.header.stamp = ros::Time::now();
    marker.ns = "detection_range";
    marker.id = 0;
    marker.type = visualization_msgs::Marker::CUBE;
    marker.action = visualization_msgs::Marker::ADD;

    // 计算实际检测区域的尺寸和位置
    // 从雷达位置开始，向前延伸指定比例的距离
    double effective_front_distance = detection_length_ * detection_front_ratio_ / 2.0;
    double effective_front_boundary = radar_x_ + effective_front_distance;
    double effective_rear_boundary = radar_x_;  // 从雷达位置开始

    // 实际检测区域的中心位置
    double actual_center_x = (effective_rear_boundary + effective_front_boundary) / 2.0;
    double actual_length = effective_front_boundary - effective_rear_boundary;

    // 位置（实际检测矩形中心）
    marker.pose.position.x = actual_center_x;
    marker.pose.position.y = radar_y_;
    marker.pose.position.z = (min_height_ + max_height_) / 2.0;
    marker.pose.orientation.w = 1.0;

    // 尺寸（实际检测长度 x 宽度 x 高度）
    marker.scale.x = actual_length;
    marker.scale.y = detection_width_;
    marker.scale.z = max_height_ - min_height_;

    // 颜色（半透明蓝色）
    marker.color.r = 0.0;
    marker.color.g = 0.5;
    marker.color.b = 1.0;
    marker.color.a = 0.3;

    marker.lifetime = ros::Duration(0.2);

    return marker;
}

visualization_msgs::Marker ObstacleDetectorCore::createRadarPositionMarker()
{
    visualization_msgs::Marker marker;
    marker.header.frame_id = frame_id_;
    marker.header.stamp = ros::Time::now();
    marker.ns = "radar_position";
    marker.id = 1;
    marker.type = visualization_msgs::Marker::SPHERE;
    marker.action = visualization_msgs::Marker::ADD;

    // 位置
    marker.pose.position.x = radar_x_;
    marker.pose.position.y = radar_y_;
    marker.pose.position.z = radar_z_;
    marker.pose.orientation.w = 1.0;

    // 尺寸
    marker.scale.x = 0.2;
    marker.scale.y = 0.2;
    marker.scale.z = 0.2;

    // 颜色 (红色)
    marker.color.r = 1.0;
    marker.color.g = 0.0;
    marker.color.b = 0.0;
    marker.color.a = 1.0;

    marker.lifetime = ros::Duration(0.2);

    return marker;
}

std::vector<visualization_msgs::Marker> ObstacleDetectorCore::createHeightRangeMarkers()
{
    std::vector<visualization_msgs::Marker> markers;

    // 计算实际检测区域的尺寸和位置（与蓝色检测框保持一致）
    double effective_front_distance = detection_length_ * detection_front_ratio_ / 2.0;
    double effective_front_boundary = radar_x_ + effective_front_distance;
    double effective_rear_boundary = radar_x_;
    double actual_center_x = (effective_rear_boundary + effective_front_boundary) / 2.0;
    double actual_length = effective_front_boundary - effective_rear_boundary;

    // 最小高度平面
    visualization_msgs::Marker min_height_marker;
    min_height_marker.header.frame_id = frame_id_;
    min_height_marker.header.stamp = ros::Time::now();
    min_height_marker.ns = "height_range";
    min_height_marker.id = 2;
    min_height_marker.type = visualization_msgs::Marker::CUBE;  // 改为立方体以匹配检测区域
    min_height_marker.action = visualization_msgs::Marker::ADD;

    min_height_marker.pose.position.x = actual_center_x;
    min_height_marker.pose.position.y = radar_y_;
    min_height_marker.pose.position.z = min_height_;
    min_height_marker.pose.orientation.w = 1.0;

    min_height_marker.scale.x = actual_length;
    min_height_marker.scale.y = detection_width_;
    min_height_marker.scale.z = 0.02;

    min_height_marker.color.r = 1.0;
    min_height_marker.color.g = 1.0;
    min_height_marker.color.b = 0.0;
    min_height_marker.color.a = 0.3;

    min_height_marker.lifetime = ros::Duration(0.2);
    markers.push_back(min_height_marker);

    // 最大高度平面
    visualization_msgs::Marker max_height_marker;
    max_height_marker.header.frame_id = frame_id_;
    max_height_marker.header.stamp = ros::Time::now();
    max_height_marker.ns = "height_range";
    max_height_marker.id = 3;
    max_height_marker.type = visualization_msgs::Marker::CUBE;  // 改为立方体以匹配检测区域
    max_height_marker.action = visualization_msgs::Marker::ADD;

    max_height_marker.pose.position.x = actual_center_x;
    max_height_marker.pose.position.y = radar_y_;
    max_height_marker.pose.position.z = max_height_;
    max_height_marker.pose.orientation.w = 1.0;

    max_height_marker.scale.x = actual_length;
    max_height_marker.scale.y = detection_width_;
    max_height_marker.scale.z = 0.02;

    max_height_marker.color.r = 1.0;
    max_height_marker.color.g = 1.0;
    max_height_marker.color.b = 0.0;
    max_height_marker.color.a = 0.3;

    max_height_marker.lifetime = ros::Duration(0.2);
    markers.push_back(max_height_marker);

    return markers;
}

void ObstacleDetectorCore::publishFilteredPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& points,
                                                    const std_msgs::Header& header)
{
    if (points->empty())
    {
        return;
    }

    // 创建新的点云消息
    sensor_msgs::PointCloud2 filtered_cloud_msg;
    pcl::toROSMsg(*points, filtered_cloud_msg);

    filtered_cloud_msg.header.stamp = ros::Time::now();
    filtered_cloud_msg.header.frame_id = frame_id_;

    // 发布过滤后的点云
    filtered_points_pub_.publish(filtered_cloud_msg);
}

void ObstacleDetectorCore::controlCommandCallback(const auto_msgs::ControlCommandStamped::ConstPtr& ctrl_msg)
{
    // 更新当前转角
    current_steering_angle_ = ctrl_msg->cmd.steering_angle;

    // 根据转角更新动态安全框
    updateDynamicSafetyBox(current_steering_angle_);
}

void ObstacleDetectorCore::vehicleStateCallback(const std_msgs::UInt8::ConstPtr& gear_msg)
{
    current_gear_ = gear_msg->data;
    bool previous_reverse_state = is_reverse_gear_;

    // 检测倒车状态 (0x24 = R档)
    is_reverse_gear_ = (current_gear_ == 0x24);

    // 如果倒车状态发生变化，更新安全框参数
    if (previous_reverse_state != is_reverse_gear_)
    {
        updateSafetyBoxByVehicleState();

        if (is_reverse_gear_)
        {
            ROS_INFO("Vehicle entered reverse gear - switching to reverse safety box parameters");
            ROS_INFO("Reverse safety box: %.2fx%.2fm", reverse_detection_length_, reverse_detection_width_);
        }
        else
        {
            ROS_INFO("Vehicle exited reverse gear - switching to normal safety box parameters");
            ROS_INFO("Normal safety box: %.2fx%.2fm", base_detection_length_, base_detection_width_);
        }
    }
}

void ObstacleDetectorCore::updateDynamicSafetyBox(double steering_angle)
{
    // 计算转角的绝对值
    double abs_steering_angle = std::abs(steering_angle);

    // 根据当前车辆状态选择相应的参数
    double base_length, base_width, angle_threshold, scale_factor;

    if (is_reverse_gear_)
    {
        // 倒车状态使用倒车参数
        base_length = reverse_detection_length_;
        base_width = reverse_detection_width_;
        angle_threshold = reverse_steering_angle_threshold_;
        scale_factor = reverse_safety_box_scale_factor_;
    }
    else
    {
        // 正常状态使用正常参数
        base_length = base_detection_length_;
        base_width = base_detection_width_;
        angle_threshold = steering_angle_threshold_;
        scale_factor = safety_box_scale_factor_;
    }

    // 判断是否超过转角阈值
    if (abs_steering_angle > angle_threshold)
    {
        // 转弯时缩小安全框
        detection_length_ = base_length * scale_factor;
        detection_width_ = base_width * scale_factor;

        ROS_DEBUG_THROTTLE(1.0, "%s turning detected (angle=%.3f rad), safety box scaled to %.2fx%.2fm",
                          is_reverse_gear_ ? "Reverse" : "Forward", steering_angle, detection_length_, detection_width_);
    }
    else
    {
        // 直行时使用基础安全框尺寸
        detection_length_ = base_length;
        detection_width_ = base_width;

        ROS_DEBUG_THROTTLE(5.0, "%s straight driving (angle=%.3f rad), safety box restored to %.2fx%.2fm",
                          is_reverse_gear_ ? "Reverse" : "Forward", steering_angle, detection_length_, detection_width_);
    }
}

void ObstacleDetectorCore::updateSafetyBoxByVehicleState()
{
    // 根据车辆状态更新安全框基础参数
    if (is_reverse_gear_)
    {
        // 倒车状态：使用倒车安全框参数
        detection_length_ = reverse_detection_length_;
        detection_width_ = reverse_detection_width_;
        detection_front_ratio_ = reverse_detection_front_ratio_;
    }
    else
    {
        // 正常状态：使用正常安全框参数
        detection_length_ = base_detection_length_;
        detection_width_ = base_detection_width_;
        detection_front_ratio_ = detection_front_ratio_;
    }

    // 重新应用转角调整
    updateDynamicSafetyBox(current_steering_angle_);
}

} // namespace obstacle_detector
