# IMM-UKF-PDA Tracker

Autoware package based on IMM-UKF-PDA tracker.

* From a sourced terminal:

`roslaunch lidar_tracker imm_ukf_pda_tracker.launch`


* From Runtime Manager:

Computing Tab -> Detection/ lidar_detector -> `imm_ukf_pda_tracker`


### Reference
A<PERSON> <PERSON><PERSON>, 3D-LIDAR Multi Object Tracking for Autonomous Driving. 2017. [paper](https://repository.tudelft.nl/islandora/object/uuid:f536b829-42ae-41d5-968d-13bbaa4ec736)

<PERSON><PERSON>, Bayesian environment representation, prediction, and criticality assessment for driver assistance systems. 2017. [paper](https://www.researchgate.net/publication/313463578_Bayesian_environment_representation_prediction_and_criticality_assessment_for_driver_assistance_systems)

### Requirements
* `eucledian_cluster` node.
* `ray_ground_filter` node.
* `/tf` topic. Below video is from Suginami data which contais /tf topic: (`autoware-20180205150908.bag`). You can download it from ROSBAG STORE for free. Otherwise, you need to do localization with a map to produce /tf topic from `velodyne` to `world`.
* `wayarea` info from vectormap if is possible.

### Parameters

Launch file available parameters for `imm_ukf_pda_tracker`

|Parameter| Type| Description|
----------|-----|--------
|`tracker_input_topic`|*String* |Input topic(type: auto_msgs::CloudClusterArray). Default `/cloud cluster`.|
|`tracker_output_topic`|*String*|Output topic(type: auto_msgs::CloudClusterArray). Default `/tracking_cluster_array`.|
|`life_time_threshold`|*Int*|The minimum frames for targets to be visualized. Default `8`.|
|`gating_threshold`|*Double*|The value of gate threshold for measurement validation. Default `9.22`.|
|`gate_probability`|*Double*|The probability that the gate contains the true measurement. Default `0.99`.|
|`detection_probability`|*Double*|The probability that a target is detected. Default `0.9`.|
|`merge_distance_threshold`|*Double*|The distance threshold for associating bounding box over frames. Default `0.5`.|
|`static_velocity_threshold`|*Double*|The velocity threshold for classifying static/dynamic. Default `0.5`.|
|`static_num_history_threshold`|*Int*|The amount of frames the velocity is averaged over to compare to `static_velocity_threshold`. Default `3`.|
|`prevent_explosion_threshold`|*Double*|The threshold for stopping kalman filter update. Default `1000`.|
|`use_sukf`|*bool*|Use standard kalman filter. Default `false`.|

Launch file available parameters for `visualize_detected_objects`

|Parameter| Type| Description|
----------|-----|--------
|`input_topic`|*String* |Input topic(type: auto_msgs::CloudClusterArray). Default `/tracking_cluster_array`.|
|`pointcloud frame`|*String*|Pointcloud frame. Default `velodyne`.|


### Subscribed topics
Node: imm_ukf_pda_tracker

|Topic|Type|Objective|
------|----|---------
|`/detection/lidar_objects`|`auto_msgs::DetectedObjectArray`|Segmented pointcloud from a clustering algorithm like eucledian cluster.|
|`/tf`|`tf`|Tracking objects in `world` coordinate.|

Node: visualize_detected_objects

|Topic|Type|Objective|
------|----|---------
|`/detected_objects`|`auto_msgs::DetectedObjectArray`|Objects with tracking info.|

### Published topics

Node: imm_ukf_pda_tracker

|Topic|Type|Objective|
------|----|---------
|`/detected_objects`|`auto_msgs::DetectedObjectArray`|Added info like velocity, yaw ,yaw_rate and static/dynamic class to DetectedObject msg.|
|`/bounding_boxes_tracked`|`jsk_recognition_msgs::BoundingBoxArray`|Visualze bounsing box nicely in rviz by JSK bounding box. Label contains information about static/dynamic class|

Node: visualize_detected_objects

|Topic|Type|Objective|
------|----|---------
|`/detected_objects/velocity_arrow`|`visualization_msgs::Marker`|Visualize velocity and yaw of the targets.|
|`/detected_objects/target_id`|`visualization_msgs::Marker`|Visualize targets' id.|



### Video

[![IMM UKF PDA lidar_tracker Autoware](https://img.youtube.com/vi/tKgDVsIfH-s/0.jpg)](https://youtu.be/tKgDVsIfH-s)


### Benchmark
Please notice that benchmark scripts are in another repository.
You can tune parameters by using benchmark based on KITTI dataset.
The repository is [here](https://github.com/cirpue49/kitti_tracking_benchmark).
