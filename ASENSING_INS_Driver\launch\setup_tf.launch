<launch>
  <arg name="x" default="0.5" />
  <arg name="y" default="0.0" />
  <arg name="z" default="1.2" />
  <arg name="yaw" default="0.0" />
  <arg name="pitch" default="0.0" />
  <arg name="roll" default="0.0" />
  <arg name="frame_id" default="base_link" />
  <arg name="child_frame_id" default="velodyne" />
  <arg name="period_in_ms" default="10" />

  <node pkg="tf" type="static_transform_publisher" name="base_link_to_localizer" args="$(arg x) $(arg y) $(arg z) $(arg yaw) $(arg pitch) $(arg roll) $(arg frame_id) $(arg child_frame_id) $(arg period_in_ms)"/>

</launch>
