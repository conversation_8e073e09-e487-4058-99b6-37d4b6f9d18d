^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package health_checker
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

1.11.0 (2019-03-21)
-------------------
* fix thread handling of node_status_publisher. (`#2058 <https://github.com/CPFL//issues/2058>`_)
* Revert "Fix/health checker (`#2012 <https://github.com/CPFL//issues/2012>`_)" (`#2037 <https://github.com/CPFL//issues/2037>`_)
  This reverts commit e4187a7138eb90ad6f119eb35f824b16465aefda.
  Reverts `#2012 <https://github.com/CPFL//issues/2012>`_
  Merged without adequate description of the bug or fixes made
* Fix/health checker (`#2012 <https://github.com/CPFL//issues/2012>`_)
* Feature/ health analyzer (`#2004 <https://github.com/CPFL//issues/2004>`_)
* [Feature] enable display errors (`#1970 <https://github.com/CPFL//issues/1970>`_)
* Feature/ health checker (`#1943 <https://github.com/CPFL//issues/1943>`_)
* Contributors: Geoffrey Biggs, Masaya Kataoka, s-azumi
