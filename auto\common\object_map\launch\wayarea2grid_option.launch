<launch>
  <!-- node parameters -->
  <arg name="sensor_frame" default="velodyne" />
  <arg name="grid_frame" default="map" />
  <arg name="grid_resolution" default="0.3" />
  <arg name="grid_length_x" default="150" />
  <arg name="grid_length_y" default="150" />
  <arg name="grid_position_x" default="0" />
  <arg name="grid_position_y" default="0" />
  <arg name="grid_position_z" default="-2" />
  <arg name="use_ll2" default="false" />

  <!-- Launch node -->

  <group if="$(arg use_ll2)">
    <node pkg="object_map" type="wayarea2grid_lanelet2" name="wayarea2grid_lanelet2" output="screen">
      <param name="sensor_frame" value="$(arg sensor_frame)" />
      <param name="grid_frame" value="$(arg grid_frame)" />
      <param name="grid_resolution" value="$(arg grid_resolution)" />
      <param name="grid_length_x" value="$(arg grid_length_x)" />
      <param name="grid_length_y" value="$(arg grid_length_y)" />
      <param name="grid_position_x" value="$(arg grid_position_x)" />
      <param name="grid_position_y" value="$(arg grid_position_y)" />
      <param name="grid_position_z" value="$(arg grid_position_z)" />
    </node>
  </group>

  <group unless="$(arg use_ll2)">
      <node pkg="object_map" type="wayarea2grid" name="wayarea2grid" output="screen">
        <param name="sensor_frame" value="$(arg sensor_frame)" />
        <param name="grid_frame" value="$(arg grid_frame)" />
        <param name="grid_resolution" value="$(arg grid_resolution)" />
        <param name="grid_length_x" value="$(arg grid_length_x)" />
        <param name="grid_length_y" value="$(arg grid_length_y)" />
        <param name="grid_position_x" value="$(arg grid_position_x)" />
        <param name="grid_position_y" value="$(arg grid_position_y)" />
        <param name="grid_position_z" value="$(arg grid_position_z)" />
      </node>
  </group>


</launch>
