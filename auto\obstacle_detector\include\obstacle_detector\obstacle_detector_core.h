#ifndef OBSTACLE_DETECTOR_CORE_H
#define OBSTACLE_DETECTOR_CORE_H

#include <ros/ros.h>
#include <sensor_msgs/PointCloud2.h>
#include <geometry_msgs/Point.h>
#include <visualization_msgs/Marker.h>
#include <visualization_msgs/MarkerArray.h>
#include <std_msgs/Header.h>
#include <std_msgs/Bool.h>
#include <std_msgs/UInt8.h>
#include <auto_msgs/ControlCommandStamped.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/filters/passthrough.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/kdtree/kdtree.h>

// TF功能已移除

#include <vector>
#include <string>
#include <memory>
#include <chrono>

namespace obstacle_detector
{

/**
 * @brief 障碍物检测核心类
 * 
 * 该类负责处理点云数据，检测指定范围内的障碍物，
 * 并提供可视化功能
 */
class ObstacleDetectorCore
{
public:
    /**
     * @brief 构造函数
     * @param nh ROS节点句柄
     * @param private_nh 私有节点句柄
     */
    ObstacleDetectorCore(ros::NodeHandle& nh, ros::NodeHandle& private_nh);

    /**
     * @brief 析构函数
     */
    ~ObstacleDetectorCore();

    /**
     * @brief 初始化检测器
     * @return 初始化是否成功
     */
    bool initialize();

    /**
     * @brief 点云数据回调函数
     * @param cloud_msg 输入的点云消息
     */
    void pointCloudCallback(const sensor_msgs::PointCloud2::ConstPtr& cloud_msg);

    /**
     * @brief 控制命令回调函数
     * @param ctrl_msg 控制命令消息
     */
    void controlCommandCallback(const auto_msgs::ControlCommandStamped::ConstPtr& ctrl_msg);

    /**
     * @brief 车辆状态回调函数（用于检测倒车状态）
     * @param gear_msg 档位消息
     */
    void vehicleStateCallback(const std_msgs::UInt8::ConstPtr& gear_msg);

private:
    // ROS相关
    ros::NodeHandle nh_;
    ros::NodeHandle private_nh_;
    ros::Subscriber pointcloud_sub_;
    ros::Subscriber ctrl_cmd_sub_;
    ros::Subscriber vehicle_state_sub_;
    ros::Publisher marker_pub_;
    ros::Publisher filtered_points_pub_;
    ros::Publisher obstacle_detected_pub_;
    ros::Timer visualization_timer_;

    // TF功能已移除

    // 矩形检测参数
    int point_threshold_;           ///< 点云数量阈值
    double detection_length_;       ///< 检测长度(米) - 前后方向
    double detection_width_;        ///< 检测宽度(米) - 左右方向
    double radar_x_;                ///< 雷达X坐标
    double radar_y_;                ///< 雷达Y坐标
    double radar_z_;                ///< 雷达Z坐标
    double min_height_;             ///< 最小高度过滤
    double max_height_;             ///< 最大高度过滤
    std::string frame_id_;          ///< 坐标系
    bool enable_visualization_;     ///< 是否启用可视化
    std::string input_topic_;       ///< 输入点云话题

    // 动态安全框参数
    double base_detection_length_;  ///< 基础检测长度(米)
    double base_detection_width_;   ///< 基础检测宽度(米)
    double steering_angle_threshold_; ///< 转角阈值(弧度)
    double safety_box_scale_factor_; ///< 安全框缩放因子
    double current_steering_angle_;  ///< 当前转角
    double detection_front_ratio_;   ///< 前向检测比例(0.5=前半部分, 0.33=前1/3)

    // 状态变量
    std::chrono::steady_clock::time_point last_warning_time_;
    double warning_interval_;       ///< 警告间隔时间(秒)

    // PCL相关
    pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud_;

    /**
     * @brief 加载参数
     */
    void loadParameters();

    /**
     * @brief 提取指定范围内的点云
     * @param input_cloud 输入点云
     * @param output_cloud 输出点云
     * @return 范围内的点云数量
     */
    int extractPointsInRange(const pcl::PointCloud<pcl::PointXYZ>::Ptr& input_cloud,
                            pcl::PointCloud<pcl::PointXYZ>::Ptr& output_cloud);

    /**
     * @brief 触发障碍物警告
     * @param point_count 检测到的点云数量
     * @param points 检测到的点云
     */
    void triggerWarning(int point_count, const pcl::PointCloud<pcl::PointXYZ>::Ptr& points);

    /**
     * @brief 分析障碍物分布
     * @param points 检测到的点云
     * @return 分布描述字符串
     */
    std::string analyzeObstacleDistribution(const pcl::PointCloud<pcl::PointXYZ>::Ptr& points);

    /**
     * @brief 发布可视化标记
     * @param event 定时器事件
     */
    void publishVisualization(const ros::TimerEvent& event);



    /**
     * @brief 创建检测范围矩形标记
     * @return 矩形标记
     */
    visualization_msgs::Marker createDetectionRectangleMarker();

    /**
     * @brief 创建雷达位置标记
     * @return 标记消息
     */
    visualization_msgs::Marker createRadarPositionMarker();

    /**
     * @brief 创建高度范围标记
     * @return 标记数组
     */
    std::vector<visualization_msgs::Marker> createHeightRangeMarkers();

    /**
     * @brief 发布过滤后的点云
     * @param points 过滤后的点云
     * @param header 原始消息头
     */
    void publishFilteredPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& points,
                                  const std_msgs::Header& header);

    /**
     * @brief 计算两点间距离
     * @param p1 点1
     * @param p2 点2
     * @return 距离
     */
    double calculateDistance(const pcl::PointXYZ& p1, const pcl::PointXYZ& p2);

    /**
     * @brief 检查时间间隔
     * @return 是否可以发出警告
     */
    bool checkWarningInterval();

    /**
     * @brief 根据转角更新动态安全框
     * @param steering_angle 当前转角(弧度)
     */
    void updateDynamicSafetyBox(double steering_angle);


};

} // namespace obstacle_detector

#endif // OBSTACLE_DETECTOR_CORE_H
