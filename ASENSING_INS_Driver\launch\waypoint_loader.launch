<?xml version="1.0"?>
<launch>
  <arg name="load_csv" default="true" />
  <arg name="multi_lane_csv" default="$(find ins)/config/saved_waypoints.csv" />
  <arg name="replanning_mode" default="False" />
  <arg name="realtime_tuning_mode" default="False" />
  <arg name="resample_mode" default="False" />
  <arg name="resample_interval" default="1.0" />
  <arg name="replan_curve_mode" default="False" />
  <arg name="overwrite_vmax_mode" default="False" />
  <arg name="replan_endpoint_mode" default="True" />
  <arg name="velocity_max" default="20" />
  <arg name="radius_thresh" default="20" />
  <arg name="radius_min" default="6" />
  <arg name="velocity_min" default="4" />
  <arg name="accel_limit" default="0.5" />
  <arg name="decel_limit" default="0.3" />
  <arg name="velocity_offset" default="4" />
  <arg name="braking_distance" default="5" />
  <arg name="end_point_offset" default="1" />
  <arg name="use_decision_maker" default="false" />

  <!-- rosrun waypoint_maker waypoint_loader _multi_lane_csv:="path file" -->
  <node pkg="waypoint_maker" type="waypoint_loader" name="waypoint_loader" output="screen" if="$(arg load_csv)">
  <!-- <rosparam command="load" file="$(find ins)/config/map.yaml"/> -->
    <param name="multi_lane_csv" value="$(arg multi_lane_csv)" />
  </node>
  <node pkg="waypoint_maker" type="waypoint_replanner" name="waypoint_replanner" output="screen">
    <param name="replanning_mode" value="$(arg replanning_mode)" />
    <param name="realtime_tuning_mode" value="$(arg realtime_tuning_mode)" />
    <param name="resample_mode" value="$(arg resample_mode)" />
    <param name="resample_interval" value="$(arg resample_interval)" />
    <param name="replan_curve_mode" value="$(arg replan_curve_mode)" />
    <param name="overwrite_vmax_mode" value="$(arg overwrite_vmax_mode)" />
    <param name="replan_endpoint_mode" value="$(arg replan_endpoint_mode)" />
    <param name="velocity_max" value="$(arg velocity_max)" />
    <param name="radius_thresh" value="$(arg radius_thresh)" />
    <param name="radius_min" value="$(arg radius_min)" />
    <param name="velocity_min" value="$(arg velocity_min)" />
    <param name="accel_limit" value="$(arg accel_limit)" />
    <param name="decel_limit" value="$(arg decel_limit)" />
    <param name="velocity_offset" value="$(arg velocity_offset)" />
    <param name="braking_distance" value="$(arg braking_distance)" />
    <param name="end_point_offset" value="$(arg end_point_offset)" />
    <param name="use_decision_maker" value="$(arg use_decision_maker)" />
  </node>
  <node pkg="waypoint_maker" type="waypoint_marker_publisher" name="waypoint_marker_publisher" />
</launch>
