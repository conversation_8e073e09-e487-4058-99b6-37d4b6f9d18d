
/// \file DecisionMaker.cpp
/// \brief Initialize behaviors state machine, and calculate required parameters for the state machine transition conditions
/// \author <PERSON><PERSON>
/// \date Dec 14, 2016

#include "op_planner/DecisionMaker.h"
#include "op_utility/UtilityH.h"
#include "op_planner/PlanningHelpers.h"
#include "op_planner/MappingHelpers.h"
#include "op_planner/MatrixOperations.h"

namespace PlannerHNS
{

  DecisionMaker::DecisionMaker()
  {
    m_iCurrentTotalPathId = 0;
    pLane = 0;
    m_pCurrentBehaviorState = 0;
    m_pGoToGoalState = 0;
    m_pStopState = 0;
    m_pWaitState = 0;
    m_pMissionCompleteState = 0;
    m_pAvoidObstacleState = 0;
    m_pTrafficLightStopState = 0;
    m_pTrafficLightWaitState = 0;
    m_pStopSignStopState = 0;
    m_pStopSignWaitState = 0;
    m_pFollowState = 0;
    m_MaxLaneSearchDistance = 3.0;
    m_pStopState = 0;
    m_pMissionCompleteState = 0;
    m_pGoalState = 0;
    m_pGoToGoalState = 0;
    m_pWaitState = 0;
    m_pInitState = 0;
    m_pFollowState = 0;
    m_pAvoidObstacleState = 0;
  }

  DecisionMaker::~DecisionMaker()
  {
    delete m_pStopState;
    delete m_pMissionCompleteState;
    delete m_pGoalState;
    delete m_pGoToGoalState;
    delete m_pWaitState;
    delete m_pInitState;
    delete m_pFollowState;
    delete m_pAvoidObstacleState;
    delete m_pTrafficLightStopState;
    delete m_pTrafficLightWaitState;
    delete m_pStopSignWaitState;
    delete m_pStopSignStopState;
  }

  void DecisionMaker::Init(const ControllerParams &ctrlParams, const PlannerHNS::PlanningParams &params, const CAR_BASIC_INFO &carInfo)
  {
    m_CarInfo = carInfo;
    m_ControlParams = ctrlParams;
    m_params = params;

    m_pidVelocity.Init(0.01, 0.004, 0.01);
    m_pidVelocity.Setlimit(m_params.maxSpeed, 0);

    m_pidStopping.Init(0.05, 0.05, 0.1);
    m_pidStopping.Setlimit(m_params.horizonDistance, 0);

    m_pidFollowing.Init(0.05, 0.05, 0.01);
    m_pidFollowing.Setlimit(m_params.minFollowingDistance, 0);

    InitBehaviorStates();

    if (m_pCurrentBehaviorState)
      m_pCurrentBehaviorState->SetBehaviorsParams(&m_params);
  }

  void DecisionMaker::InitBehaviorStates()
  {

    m_pStopState = new StopState(&m_params, 0, 0);
    m_pMissionCompleteState = new MissionAccomplishedStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), 0);
    m_pGoalState = new GoalStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pMissionCompleteState);
    m_pGoToGoalState = new ForwardStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pGoalState);
    m_pInitState = new InitStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pGoToGoalState);

    m_pFollowState = new FollowStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pGoToGoalState);
    m_pAvoidObstacleState = new SwerveStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pGoToGoalState);
    m_pStopSignWaitState = new StopSignWaitStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pGoToGoalState);
    m_pStopSignStopState = new StopSignStopStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pStopSignWaitState);

    m_pTrafficLightWaitState = new TrafficLightWaitStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pGoToGoalState);
    m_pTrafficLightStopState = new TrafficLightStopStateII(m_pStopState->m_pParams, m_pStopState->GetCalcParams(), m_pGoToGoalState);

    m_pGoToGoalState->InsertNextState(m_pAvoidObstacleState);
    m_pGoToGoalState->InsertNextState(m_pStopSignStopState);
    m_pGoToGoalState->InsertNextState(m_pTrafficLightStopState);
    m_pGoToGoalState->InsertNextState(m_pFollowState);
    m_pGoToGoalState->decisionMakingCount = 0; // m_params.nReliableCount;

    m_pGoalState->InsertNextState(m_pGoToGoalState);

    m_pStopSignWaitState->decisionMakingTime = m_params.stopSignStopTime;
    m_pStopSignWaitState->InsertNextState(m_pStopSignStopState);
    m_pStopSignWaitState->InsertNextState(m_pGoalState);

    m_pTrafficLightStopState->InsertNextState(m_pTrafficLightWaitState);

    m_pTrafficLightWaitState->InsertNextState(m_pTrafficLightStopState);
    m_pTrafficLightWaitState->InsertNextState(m_pGoalState);

    m_pFollowState->InsertNextState(m_pAvoidObstacleState);
    m_pFollowState->InsertNextState(m_pStopSignStopState);
    m_pFollowState->InsertNextState(m_pTrafficLightStopState);
    m_pFollowState->InsertNextState(m_pGoalState);
    m_pFollowState->decisionMakingCount = 0; // m_params.nReliableCount;

    m_pInitState->decisionMakingCount = 0; // m_params.nReliableCount;

    m_pCurrentBehaviorState = m_pInitState;
  }

  bool DecisionMaker::GetNextTrafficLight(const int &prevTrafficLightId, const std::vector<PlannerHNS::TrafficLight> &trafficLights, PlannerHNS::TrafficLight &trafficL)
  {
    for (unsigned int i = 0; i < trafficLights.size(); i++)
    {
      double d = hypot(trafficLights.at(i).pos.y - state.pos.y, trafficLights.at(i).pos.x - state.pos.x);
      if (d <= trafficLights.at(i).stoppingDistance)
      {
        double a_diff = UtilityHNS::UtilityH::AngleBetweenTwoAnglesPositive(UtilityHNS::UtilityH::FixNegativeAngle(trafficLights.at(i).pos.a), UtilityHNS::UtilityH::FixNegativeAngle(state.pos.a));

        if (a_diff < M_PI_2 && trafficLights.at(i).id != prevTrafficLightId)
        {
          // std::cout << "Detected Light, ID = " << trafficLights.at(i).id << ", Distance = " << d << ", Angle = " << trafficLights.at(i).pos.a*RAD2DEG << ", Car Heading = " << state.pos.a*RAD2DEG << ", Diff = " << a_diff*RAD2DEG << std::endl;
          trafficL = trafficLights.at(i);
          return true;
        }
      }
    }

    return false;
  }

  void DecisionMaker::CalculateImportantParameterForDecisionMaking(const PlannerHNS::VehicleState &car_state,
                                                                   const int &goalID, const bool &bEmergencyStop, const std::vector<TrafficLight> &detectedLights,
                                                                   const TrajectoryCost &bestTrajectory)
  {
    // 如果路径长度为零，则直接返回，表示没有路径数据处理
    if (m_TotalPath.size() == 0)
      return;

    // 获取当前行为状态的计算参数
    PreCalculatedConditions *pValues = m_pCurrentBehaviorState->GetCalcParams();

    // 如果车辆最大减速度不为零，则计算最小停车距离
    if (m_CarInfo.max_deceleration != 0)
      pValues->minStoppingDistance = -pow(car_state.speed, 2) / (m_CarInfo.max_deceleration);

    // 设置中央轨迹索引，通常为轨迹总数的一半
    pValues->iCentralTrajectory = m_pCurrentBehaviorState->m_pParams->rollOutNumber / 2;

    // 如果之前没有安全轨迹，则将中央轨迹设为当前安全轨迹
    if (pValues->iPrevSafeTrajectory < 0)
      pValues->iPrevSafeTrajectory = pValues->iCentralTrajectory;

    // 清除之前的停车距离列表，准备重新计算
    pValues->stoppingDistances.clear();

    // 记录当前速度
    pValues->currentVelocity = car_state.speed;

    // 初始化交通灯状态和相关变量
    pValues->bTrafficIsRed = false;
    pValues->currentTrafficLightID = -1;
    pValues->bFullyBlock = false;

    // 从最佳轨迹中获取到最近对象的距离和速度
    pValues->distanceToNext = bestTrajectory.closest_obj_distance;
    pValues->velocityOfNext = bestTrajectory.closest_obj_velocity;

    // 设置当前安全轨迹，如果最佳轨迹索引有效，则使用之，否则回退到中央轨迹
    if (bestTrajectory.index >= 0 && bestTrajectory.index < (int)m_RollOuts.size())
      pValues->iCurrSafeTrajectory = bestTrajectory.index;
    else
      pValues->iCurrSafeTrajectory = pValues->iCentralTrajectory;

    // 标记是否完全阻塞
    pValues->bFullyBlock = bestTrajectory.bBlocked;

    // 设置当前安全车道，如果最佳轨迹提供了有效的车道索引，则使用之
    if (bestTrajectory.lane_index >= 0)
      pValues->iCurrSafeLane = bestTrajectory.lane_index;
    else
    {
      // 否则，从总路径中获取相对信息并计算车道索引
      PlannerHNS::RelativeInfo info;
      PlannerHNS::PlanningHelpers::GetRelativeInfoRange(m_TotalPath, state, m_params.rollOutDensity * m_params.rollOutNumber / 2.0 + 0.1, info);
      pValues->iCurrSafeLane = info.iGlobalPath;
    }

    // 计算关键的车辆前端距离
    double critical_long_front_distance = m_CarInfo.wheel_base / 2.0 + m_CarInfo.length / 2.0 + m_params.verticalSafetyDistance;

    // 如果达到全局路径的终点，则更新目标ID为-1，否则使用输入的目标ID
    if (ReachEndOfGlobalPath(pValues->minStoppingDistance + critical_long_front_distance, pValues->iCurrSafeLane))
      pValues->currentGoalID = -1;
    else
      pValues->currentGoalID = goalID;

    // 更新当前总路径索引
    m_iCurrentTotalPathId = pValues->iCurrSafeLane;

    // 初始化交通信号、停车标志的相关参数
    int stopLineID = -1;
    int stopSignID = -1;
    int trafficLightID = -1;
    double distanceToClosestStopLine = 0;
    bool bGreenTrafficLight = true;

    // 计算到最近停止线的距离，并检查交通信号状态
    distanceToClosestStopLine = PlanningHelpers::GetDistanceToClosestStopLineAndCheck(m_TotalPath.at(pValues->iCurrSafeLane), state, m_params.giveUpDistance, stopLineID, stopSignID, trafficLightID) - critical_long_front_distance;

    // std::cout << "StopLineID" << stopLineID << ", StopSignID: " << stopSignID << ", TrafficLightID: " << trafficLightID << ", Distance: " << distanceToClosestStopLine << ", MinStopDistance: " << pValues->minStoppingDistance << std::endl;

    // 根据停车线的距离和交通灯状态更新行为参数
    if (distanceToClosestStopLine > m_params.giveUpDistance && distanceToClosestStopLine < (pValues->minStoppingDistance + 1.0))
    {
      if (m_pCurrentBehaviorState->m_pParams->enableTrafficLightBehavior)
      {
        pValues->currentTrafficLightID = trafficLightID;
        // std::cout << "Detected Traffic Light: " << trafficLightID << std::endl;
        for (unsigned int i = 0; i < detectedLights.size(); i++)
        {
          if (detectedLights.at(i).id == trafficLightID)
            bGreenTrafficLight = (detectedLights.at(i).lightState == GREEN_LIGHT);
        }
      }

      if (m_pCurrentBehaviorState->m_pParams->enableStopSignBehavior)
        pValues->currentStopSignID = stopSignID;

      pValues->stoppingDistances.push_back(distanceToClosestStopLine);
      // std::cout << "LP => D: " << pValues->distanceToStop() << ", PrevSignID: " << pValues->prevTrafficLightID << ", CurrSignID: " << pValues->currentTrafficLightID << ", Green: " << bGreenTrafficLight << endl;
    }

    // std::cout << "Distance To Closest: " << distanceToClosestStopLine << ", Stop LineID: " << stopLineID << ", Stop SignID: " << stopSignID << ", TFID: " << trafficLightID << std::endl;

    pValues->bTrafficIsRed = !bGreenTrafficLight;

    // 在紧急停车情况下更新阻塞状态和最近对象的距离
    if (bEmergencyStop)
    {
      pValues->bFullyBlock = true;
      pValues->distanceToNext = 1;
      pValues->velocityOfNext = 0;
    }
    // 可选的调试输出，显示各种计算的值和状态
    // cout << "Distances: " << pValues->stoppingDistances.size() << ", Distance To Stop : " << pValues->distanceToStop << endl;
  }

  void DecisionMaker::UpdateCurrentLane(const double &search_distance)
  {
    PlannerHNS::Lane *pMapLane = 0;
    PlannerHNS::Lane *pPathLane = 0;
    pPathLane = MappingHelpers::GetLaneFromPath(state, m_TotalPath.at(m_iCurrentTotalPathId));
    if (!pPathLane)
    {
      std::cout << "Performance Alert: Can't Find Lane Information in Global Path, Searching the Map :( " << std::endl;
      pMapLane = MappingHelpers::GetClosestLaneFromMap(state, m_Map, search_distance);
    }

    if (pPathLane)
      pLane = pPathLane;
    else if (pMapLane)
      pLane = pMapLane;
    else
      pLane = 0;
  }

  bool DecisionMaker::ReachEndOfGlobalPath(const double &min_distance, const int &iGlobalPathIndex)
  {
    // 检查路径数组是否为空，如果是，则直接返回 false，表示没有达到终点
    if (m_TotalPath.size() == 0)
      return false;

    PlannerHNS::RelativeInfo info;
    // 获取当前状态相对于全局路径的信息
    PlanningHelpers::GetRelativeInfo(m_TotalPath.at(iGlobalPathIndex), state, info);

    double d = 0; // 用于累计从当前位置到路径终点的距离

    // 从当前信息点的前端开始，遍历到路径的最后一个点
    for (unsigned int i = info.iFront; i < m_TotalPath.at(iGlobalPathIndex).size() - 1; i++)
    {
      // 计算相邻点之间的距离并累加到 d
      d += hypot(m_TotalPath.at(iGlobalPathIndex).at(i + 1).pos.y - m_TotalPath.at(iGlobalPathIndex).at(i).pos.y,
                 m_TotalPath.at(iGlobalPathIndex).at(i + 1).pos.x - m_TotalPath.at(iGlobalPathIndex).at(i).pos.x);
      // 如果累计距离已超过最小距离，返回 false，表示尚未接近终点
      if (d > min_distance)
        return false;
    }

    // 如果遍历完所有点后，累计距离未超过最小距离，返回 true，表示已接近或达到终点
    return true;
  }
  void DecisionMaker::SetNewGlobalPath(const std::vector<std::vector<WayPoint>> &globalPath)
  {
    if (m_pCurrentBehaviorState)
    {
      m_pCurrentBehaviorState->GetCalcParams()->bNewGlobalPath = true;
      m_TotalOriginalPath = globalPath;
    }
  }

  // 基于预先计算的参数和当前行为状态选择一个安全的轨迹。
  bool DecisionMaker::SelectSafeTrajectory()
  {
    bool bNewTrajectory = false; // 标志位，表示是否选中了新轨迹，初始为假。
    // 从当前行为状态获取预计算的参数。
    PlannerHNS::PreCalculatedConditions *preCalcPrams = m_pCurrentBehaviorState->GetCalcParams();

    // 如果预计算参数为空或没有可用的轨迹，则立即返回假。
    if (!preCalcPrams || m_RollOuts.size() == 0)
      return bNewTrajectory;

    // 获取路径中与当前状态匹配的最近下一个点的索引。
    int currIndex = PlannerHNS::PlanningHelpers::GetClosestNextPointIndexFast(m_Path, state);
    int index_limit = 0; // 索引限制，用于确定选择新轨迹的阈值。
    // 如果未设置索引限制（<=0），则将其设置为路径大小的一半。
    if (index_limit <= 0)
      index_limit = m_Path.size() / 2.0;
    // 检查当前索引是否超出限制，或是否需要重新规划或使用新的全局路径。
    if (currIndex > index_limit || preCalcPrams->bRePlan || preCalcPrams->bNewGlobalPath)
    {
      // 记录决策参数以便调试。
      std::cout << "New Local Plan !! " << currIndex << ", " << preCalcPrams->bRePlan << ", " << preCalcPrams->bNewGlobalPath << ", " << m_TotalOriginalPath.at(0).size() << ", PrevLocal: " << m_Path.size();
      // 根据行为状态确定的当前安全轨迹更新本地路径。
      m_Path = m_RollOuts.at(preCalcPrams->iCurrSafeTrajectory);
      // 记录新路径大小以便调试。
      std::cout << ", NewLocal: " << m_Path.size() << std::endl;

      // 重置标志，因为已接受新路径。
      preCalcPrams->bNewGlobalPath = false;
      preCalcPrams->bRePlan = false;
      // 标记已选择新的轨迹。
      bNewTrajectory = true;
    }

    // 返回新轨迹选择的状态。
    return bNewTrajectory;
  }

  PlannerHNS::BehaviorState DecisionMaker::GenerateBehaviorState(const PlannerHNS::VehicleState &vehicleState)
  {
    PlannerHNS::PreCalculatedConditions *preCalcPrams = m_pCurrentBehaviorState->GetCalcParams();

    m_pCurrentBehaviorState = m_pCurrentBehaviorState->GetNextState();
    if (m_pCurrentBehaviorState == 0)
      m_pCurrentBehaviorState = m_pInitState;

    PlannerHNS::BehaviorState currentBehavior;

    currentBehavior.state = m_pCurrentBehaviorState->m_Behavior;
    currentBehavior.followDistance = preCalcPrams->distanceToNext;

    currentBehavior.minVelocity = 0;
    currentBehavior.stopDistance = preCalcPrams->distanceToStop();
    currentBehavior.followVelocity = preCalcPrams->velocityOfNext;
    if (preCalcPrams->iPrevSafeTrajectory < 0 || preCalcPrams->iPrevSafeTrajectory >= m_RollOuts.size())
      currentBehavior.iTrajectory = preCalcPrams->iCurrSafeTrajectory;
    else
      currentBehavior.iTrajectory = preCalcPrams->iPrevSafeTrajectory;

    double average_braking_distance = -pow(vehicleState.speed, 2) / (m_CarInfo.max_deceleration) + m_params.additionalBrakingDistance;

    if (average_braking_distance < m_params.minIndicationDistance)
      average_braking_distance = m_params.minIndicationDistance;

    currentBehavior.indicator = PlanningHelpers::GetIndicatorsFromPath(m_Path, state, average_braking_distance);

    return currentBehavior;
  }

  double DecisionMaker::UpdateVelocityDirectlyToTrajectory(const BehaviorState &beh, const VehicleState &CurrStatus, const double &dt)
  {
    // 如果原始路径为空，则返回速度0。
    if (m_TotalOriginalPath.size() == 0)
      return 0;

    RelativeInfo info, total_info;
    // 获取当前状态相对于总路径的信息。
    PlanningHelpers::GetRelativeInfo(m_TotalOriginalPath.at(m_iCurrentTotalPathId), state, total_info);
    // 获取当前状态相对于当前路径的信息。
    PlanningHelpers::GetRelativeInfo(m_Path, state, info);

    // 计算平均制动距离。
    double average_braking_distance = -pow(CurrStatus.speed, 2) / (m_CarInfo.max_deceleration) + m_params.additionalBrakingDistance;
    // 计算最大安全速度。
    double max_velocity = PlannerHNS::PlanningHelpers::GetVelocityAhead(m_TotalOriginalPath.at(m_iCurrentTotalPathId), total_info, total_info.iBack, average_braking_distance);

    unsigned int point_index = 0;
    // 计算关键前方距离。
    double critical_long_front_distance = m_CarInfo.length / 2.0;

    // 根据行为状态选择相应的速度更新策略。
    if (beh.state == TRAFFIC_LIGHT_STOP_STATE || beh.state == STOP_SIGN_STOP_STATE)
    {
      // 根据停止距离获取路径上的跟随点。
      PlanningHelpers::GetFollowPointOnTrajectory(m_Path, info, beh.stopDistance - critical_long_front_distance, point_index);

      double e = -beh.stopDistance;
      // 使用PID控制器计算期望速度。
      double desiredVelocity = m_pidStopping.getPID(e);

      // 调整速度以确保不超过最大速度且不低于最小速度。
      if (desiredVelocity > max_velocity)
        desiredVelocity = max_velocity;
      else if (desiredVelocity < m_params.minSpeed)
        desiredVelocity = 0;

      // 更新路径上所有点的速度。
      for (unsigned int i = 0; i < m_Path.size(); i++)
        m_Path.at(i).v = desiredVelocity;

      return desiredVelocity;
    }
    else if (beh.state == FOLLOW_STATE)
    {
      // 计算关键减速值。
      double deceleration_critical = 0;
      double inv_time = 2.0 * ((beh.followDistance - (critical_long_front_distance + m_params.additionalBrakingDistance)) - CurrStatus.speed);
      if (inv_time <= 0)
        deceleration_critical = m_CarInfo.max_deceleration;
      else
        deceleration_critical = CurrStatus.speed * CurrStatus.speed / inv_time;

      if (deceleration_critical > 0)
        deceleration_critical = -deceleration_critical;
      if (deceleration_critical < -m_CarInfo.max_acceleration)
        deceleration_critical = -m_CarInfo.max_acceleration;

      // 计算期望速度。
      double desiredVelocity = (deceleration_critical * dt) + CurrStatus.speed;

      // 确保速度在允许的范围内。
      if (desiredVelocity > m_params.maxSpeed)
        desiredVelocity = m_params.maxSpeed;

      // 检查有效速度。
      if ((desiredVelocity < 0.1 && desiredVelocity > -0.1) || beh.followDistance <= 0)
        desiredVelocity = 0;

      // 更新路径上所有点的速度。
      for (unsigned int i = 0; i < m_Path.size(); i++)
        m_Path.at(i).v = desiredVelocity;

      return desiredVelocity;
    }
    // 处理其他行为状态，如直行或避障。
    else if (beh.state == FORWARD_STATE || beh.state == OBSTACLE_AVOIDANCE_STATE)
    {
      double target_velocity = max_velocity;
      bool bSlowBecauseChange = false;
      // 如果安全轨迹与中心轨迹不同，降低速度。
      if (m_pCurrentBehaviorState->GetCalcParams()->iCurrSafeTrajectory != m_pCurrentBehaviorState->GetCalcParams()->iCentralTrajectory)
      {
        target_velocity *= 0.5;
        bSlowBecauseChange = true;
      }

      double e = target_velocity - CurrStatus.speed;
      // 使用PID控制器调整速度。
      double desiredVelocity = m_pidVelocity.getPID(e);

      // 确保速度不超过最大值且不低于最小值。
      if (desiredVelocity > max_velocity)
        desiredVelocity = max_velocity;
      else if (desiredVelocity < m_params.minSpeed)
        desiredVelocity = 0;

      // 更新路径上所有点的速度。
      for (unsigned int i = 0; i < m_Path.size(); i++)
        m_Path.at(i).v = desiredVelocity;

      return desiredVelocity;
    }
    // 处理等待状态，速度设置为0。
    else if (beh.state == STOP_SIGN_WAIT_STATE || beh.state == TRAFFIC_LIGHT_WAIT_STATE)
    {
      double target_velocity = 0;
      for (unsigned int i = 0; i < m_Path.size(); i++)
        m_Path.at(i).v = target_velocity;

      return target_velocity;
    }
    // 对于其他状态，速度同样设置为0。
    else
    {
      double target_velocity = 0;
      for (unsigned int i = 0; i < m_Path.size(); i++)
        m_Path.at(i).v = target_velocity;

      return target_velocity;
    }

    // 默认返回最大速度。
    return max_velocity;
  }

  PlannerHNS::BehaviorState DecisionMaker::DoOneStep(
      const double &dt,
      const PlannerHNS::WayPoint currPose,
      const PlannerHNS::VehicleState &vehicleState,
      const int &goalID,
      const std::vector<TrafficLight> &trafficLight,
      const TrajectoryCost &tc,
      const bool &bEmergencyStop)
  {
    PlannerHNS::BehaviorState beh;
    state = currPose;
    m_TotalPath.clear();
    for (unsigned int i = 0; i < m_TotalOriginalPath.size(); i++)
    {
      t_centerTrajectorySmoothed.clear();
      PlannerHNS::PlanningHelpers::ExtractPartFromPointToDistanceDirectionFast(m_TotalOriginalPath.at(i), state, m_params.horizonDistance, m_params.pathDensity, t_centerTrajectorySmoothed);
      m_TotalPath.push_back(t_centerTrajectorySmoothed);
    }

    if (m_TotalPath.size() == 0)
      return beh;

    UpdateCurrentLane(m_MaxLaneSearchDistance);

    CalculateImportantParameterForDecisionMaking(vehicleState, goalID, bEmergencyStop, trafficLight, tc);

    beh = GenerateBehaviorState(vehicleState);

    beh.bNewPlan = SelectSafeTrajectory();

    beh.maxVelocity = UpdateVelocityDirectlyToTrajectory(beh, vehicleState, dt);

    // std::cout << "Eval_i: " << tc.index << ", Curr_i: " <<  m_pCurrentBehaviorState->GetCalcParams()->iCurrSafeTrajectory << ", Prev_i: " << m_pCurrentBehaviorState->GetCalcParams()->iPrevSafeTrajectory << std::endl;

    return beh;
  }

} /* namespace PlannerHNS */
