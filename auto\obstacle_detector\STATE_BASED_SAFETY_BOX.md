# 状态安全框功能说明

## 概述

本功能为obstacle_detector模块实现了基于车辆状态的安全框系统。系统能够根据usbcan的档位状态（正常驾驶/倒车）自动切换不同的安全框参数，确保在不同驾驶模式下的安全性。

## 功能特性

### 1. 状态检测
- 通过订阅`/vehicle_gear`话题获取车辆档位信息
- 自动检测倒车状态（档位 = 0x24）
- 实时状态切换，无延迟

### 2. 双模式安全框
- **正常模式**：当车辆不处于倒车状态时使用
- **倒车模式**：当车辆处于倒车状态（R档）时使用

### 3. 独立参数配置
每种模式都有独立的安全框参数：
- 检测长度和宽度
- 转角阈值
- 安全框缩放因子
- 前向检测比例

## 配置参数

### 正常模式参数
```xml
<!-- 正常状态矩形检测参数 -->
<param name="detection_length" value="7.0" />      <!-- 基础检测长度(米) -->
<param name="detection_width" value="1.5" />       <!-- 基础检测宽度(米) -->

<!-- 正常状态动态安全框参数 -->
<param name="steering_angle_threshold" value="0.1" />  <!-- 转角阈值(弧度) -->
<param name="safety_box_scale_factor" value="0.5" />   <!-- 转弯时缩放因子 -->
<param name="detection_front_ratio" value="1" />       <!-- 前向检测比例 -->
```

### 倒车模式参数
```xml
<!-- 倒车状态安全框参数 -->
<param name="reverse_detection_length" value="3.0" />      <!-- 倒车检测长度(米) -->
<param name="reverse_detection_width" value="1.2" />       <!-- 倒车检测宽度(米) -->
<param name="reverse_steering_angle_threshold" value="0.15" />  <!-- 倒车转角阈值 -->
<param name="reverse_safety_box_scale_factor" value="0.6" />    <!-- 倒车转弯缩放因子 -->
<param name="reverse_detection_front_ratio" value="0.8" />      <!-- 倒车前向检测比例 -->
```

## 工作原理

### 1. 状态监听
- VehicleController发布当前档位信息到`/vehicle_gear`话题
- ObstacleDetectorCore订阅该话题并实时更新车辆状态

### 2. 参数切换
当检测到档位变化时：
1. 更新`is_reverse_gear_`状态标志
2. 调用`updateSafetyBoxByVehicleState()`切换基础参数
3. 重新应用当前转角的动态调整

### 3. 动态调整
在每种模式下，系统仍然支持基于转角的动态安全框调整：
- 直行时使用基础尺寸
- 转弯时根据缩放因子缩小安全框

## 话题接口

### 订阅话题
- `/vehicle_gear` (std_msgs/UInt8): 车辆档位信息
  - 0x21: D档（前进）
  - 0x22: N档（空档）
  - 0x24: R档（倒车）

### 发布话题
- `/obstacle_detected` (std_msgs/Bool): 障碍物检测状态
- `/obstacle_detection_markers` (visualization_msgs/MarkerArray): 可视化标记
- `/filtered_points` (sensor_msgs/PointCloud2): 过滤后的点云

## 日志输出

系统会在状态切换时输出信息日志：
```
[INFO] Vehicle entered reverse gear - switching to reverse safety box parameters
[INFO] Reverse safety box: 3.00x1.20m

[INFO] Vehicle exited reverse gear - switching to normal safety box parameters  
[INFO] Normal safety box: 7.00x1.50m
```

## 使用方法

1. 确保VehicleController正常运行并发布档位信息
2. 启动obstacle_detector节点：
   ```bash
   roslaunch obstacle_detector obstacle_detector.launch
   ```
3. 系统将自动根据车辆档位切换安全框模式

## 安全考虑

- 倒车模式使用更小的安全框尺寸，适合低速精确操作
- 倒车模式的转角阈值更高，减少不必要的安全框缩放
- 状态切换是实时的，确保安全性不受影响
- 保持了原有的动态调整功能，在转弯时进一步提高安全性

## 调试

使用以下命令查看档位状态：
```bash
rostopic echo /vehicle_gear
```

使用以下命令查看障碍物检测状态：
```bash
rostopic echo /obstacle_detected
```

在RViz中可以看到安全框的可视化效果，不同模式下安全框大小会有明显区别。
