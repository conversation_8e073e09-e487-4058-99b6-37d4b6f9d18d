# CAN数据记录配置文件
# CAN Data Logging Configuration

# 日志文件切换间隔 (秒)
# Log file rotation interval (seconds)
log_rotation_interval: 60

# 日志文件保留天数
# Log file retention days
log_retention_days: 1

# 是否启用CAN数据记录
# Enable CAN data logging
enable_logging: true

# 日志文件目录 (相对于包路径)
# Log file directory (relative to package path)
log_directory: "can_logs"

# 日志文件名前缀
# Log file name prefix
log_file_prefix: "can_data"

# 是否启用实时日志刷新 (确保数据立即写入磁盘)
# Enable real-time log flushing (ensure data is written to disk immediately)
enable_real_time_flush: true

# 单个日志文件最大大小 (MB)
# Maximum size per log file (MB)
max_log_file_size_mb: 100

# 需要记录的CAN ID列表 (空列表表示记录所有)
# List of CAN IDs to log (empty list means log all)
can_ids_to_log: [0x18F, 0x285, 0x286, 0x287, 0x288, 0x314, 0x721, 0x731, 0x7B9]
  # 示例：只记录这些指定的CAN ID
  # Example: Only log these specified CAN IDs
  # 如果要记录所有ID，请设置为空列表: []
  # To log all IDs, set to empty list: []

# 需要过滤的CAN ID列表 (这些ID不会被记录)
# List of CAN IDs to filter out (these IDs will not be logged)
can_ids_to_filter: []
  # - 0x123
  # - 0x456