<?xml version="1.0"?>
<launch>

  <!-- Debug version with more relaxed parameters and enhanced logging -->
  
  <!-- Navi configurations -->
  <arg name="costmap_topic" default="semantics/costmap_generator/occupancy_grid" />
  <arg name="waypoints_velocity" default="5.0" />
  <arg name="update_rate" default="1.0" />

  <!-- A* search configurations - Extremely relaxed for debugging -->
  <arg name="use_back" default="true" />
  <arg name="use_potential_heuristic" default="true" />
  <arg name="use_wavefront_heuristic" default="false" />
  <arg name="time_limit" default="20000.0" />  <!-- Very long time limit -->
  <arg name="robot_length" default="4.5" />
  <arg name="robot_width" default="1.75" />
  <arg name="robot_base2back" default="1.0" />
  <arg name="minimum_turning_radius" default="6.0" />
  <arg name="theta_size" default="36" />  <!-- Reduced for faster search -->
  <arg name="curve_weight" default="0.8" />  <!-- Very low curve penalty -->
  <arg name="reverse_weight" default="1.2" />  <!-- Very low reverse penalty -->
  <arg name="lateral_goal_range" default="3.0" />  <!-- Extremely relaxed goal tolerance -->
  <arg name="longitudinal_goal_range" default="5.0" />  <!-- Extremely relaxed goal tolerance -->
  <arg name="angle_goal_range" default="45.0" />  <!-- Extremely relaxed angle tolerance -->
  <arg name="obstacle_threshold" default="20" />  <!-- Very low obstacle threshold -->
  <arg name="potential_weight" default="2.0" />  <!-- Very low potential weight -->
  <arg name="distance_heuristic_weight" default="0.5" />

  <node pkg="freespace_planner" type="astar_navi" name="astar_navi" output="screen">
    <remap from="costmap" to="$(arg costmap_topic)" />
    <param name="waypoints_velocity" value="$(arg waypoints_velocity)" />
    <param name="update_rate" value="$(arg update_rate)" />

    <param name="use_back" value="$(arg use_back)" />
    <param name="use_potential_heuristic" value="$(arg use_potential_heuristic)" />
    <param name="use_wavefront_heuristic" value="$(arg use_wavefront_heuristic)" />
    <param name="time_limit" value="$(arg time_limit)" />
    <param name="robot_length" value="$(arg robot_length)" />
    <param name="robot_width" value="$(arg robot_width)" />
    <param name="robot_base2back" value="$(arg robot_base2back)" />
    <param name="minimum_turning_radius" value="$(arg minimum_turning_radius)" />
    <param name="theta_size" value="$(arg theta_size)" />
    <param name="angle_goal_range" value="$(arg angle_goal_range)" />
    <param name="curve_weight" value="$(arg curve_weight)" />
    <param name="reverse_weight" value="$(arg reverse_weight)" />
    <param name="lateral_goal_range" value="$(arg lateral_goal_range)" />
    <param name="longitudinal_goal_range" value="$(arg longitudinal_goal_range)" />
    <param name="obstacle_threshold" value="$(arg obstacle_threshold)" />
    <param name="potential_weight" value="$(arg potential_weight)" />
    <param name="distance_heuristic_weight" value="$(arg distance_heuristic_weight)" />
  </node>

  <!-- Visualization node-->
  <node pkg="waypoint_maker" type="waypoint_marker_publisher" name="waypoint_marker_publisher" />

</launch>
