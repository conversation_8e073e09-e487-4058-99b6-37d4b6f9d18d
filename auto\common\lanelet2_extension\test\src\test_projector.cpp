/*
 * Copyright 2015-2019  Foundation. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <ros/ros.h>

#include <gtest/gtest.h>
#include <math.h>

#include <lanelet2_extension/projection/mgrs_projector.h>
#include <lanelet2_extension/projection/enu_projector.h>

class TestSuite : public ::testing::Test
{
public:
  TestSuite()
  {
    // Origin point
    origin_gps.lat = 35.652832000;
    origin_gps.lon = 139.839478000;
    origin_gps.ele = 0.0;

    // Test point that is 111m east of origin
    east_gps.lat = 35.652831994;
    east_gps.lon = 139.840703744;
    east_gps.ele = 0.0;
  }

  ~TestSuite()
  {
  }

  lanelet::GPSPoint origin_gps;
  lanelet::GPSPoint east_gps;
};

// MGRS Projector
TEST_F(TestSuite, ForwardMgrsProjection)
{
  lanelet::projection::MGRSProjector projector;
  // lat/lon in Tokyo
  lanelet::GPSPoint gps_point;
  gps_point.lat = 35.652832;
  gps_point.lon = 139.839478;
  gps_point.ele = 12.3456789;
  lanelet::BasicPoint3d mgrs_point = projector.forward(gps_point);

  // projected z value should not change
  ASSERT_DOUBLE_EQ(mgrs_point.z(), gps_point.ele) << "Forward projected z value should be " << gps_point.ele;

  // https://www.movable-type.co.uk/scripts/latlong-utm-mgrs.html
  // round the projected value to mm since the above reference only gives value
  // in mm precision
  ASSERT_EQ(projector.getProjectedMGRSGrid(), "54SUE") << "Projected grid should be "
                                                       << "54SUE";
  double rounded_x_mm = round(mgrs_point.x() * 1000) / 1000.0;
  ASSERT_DOUBLE_EQ(rounded_x_mm, 94946.081) << "Forward projected x value should be " << 94946.081;
  double rounded_y_mm = round(mgrs_point.y() * 1000) / 1000.0;
  ASSERT_DOUBLE_EQ(rounded_y_mm, 46063.748) << "Forward projected y value should be " << 46063.748;
}

TEST_F(TestSuite, ReverseMgrsProjection)
{
  lanelet::projection::MGRSProjector projector;
  lanelet::BasicPoint3d mgrs_point;
  mgrs_point.x() = 94946.0;
  mgrs_point.y() = 46063.0;
  mgrs_point.z() = 12.3456789;

  projector.setMGRSCode("54SUE");
  lanelet::GPSPoint gps_point = projector.reverse(mgrs_point);

  // projected z value should not change
  ASSERT_DOUBLE_EQ(gps_point.ele, mgrs_point.z()) << "Reverse projected z value should be " << mgrs_point.z();

  // https://www.movable-type.co.uk/scripts/latlong-utm-mgrs.html
  // round the projected value since the above reference only gives value up to
  // precision of 1e-8
  double rounded_lat = round(gps_point.lat * 1e8) / 1e8;
  ASSERT_DOUBLE_EQ(rounded_lat, 35.65282525) << "Reverse projected latitude value should be " << 35.65282525;
  double rounded_lon = round(gps_point.lon * 1e8) / 1e8;
  ASSERT_DOUBLE_EQ(rounded_lon, 139.83947721) << "Reverse projected longitude value should be " << 139.83947721;
}

// ENU Projector
TEST_F(TestSuite, ForwardEnuProjection)
{
  lanelet::Origin origin(origin_gps);
  lanelet::projection::EnuProjector enu_projector(origin);

  // Origin point
  lanelet::BasicPoint3d enu_point = enu_projector.forward(origin_gps);

  ASSERT_NEAR(enu_point.x(), 0., 0.001);
  ASSERT_NEAR(enu_point.y(), 0., 0.001);
  ASSERT_NEAR(enu_point.z(), 0., 0.001);

  // Test point 111m east
  enu_point = enu_projector.forward(east_gps);
  ASSERT_NEAR(enu_point.x(), 111., 0.001);
  ASSERT_NEAR(enu_point.y(), 0., 0.001);
  ASSERT_NEAR(enu_point.z(), 0., 0.001);
}

TEST_F(TestSuite, ReverseEnuProjection)
{
  lanelet::Origin origin(origin_gps);
  lanelet::projection::EnuProjector enu_projector(origin);

  // Origin point
  lanelet::GPSPoint gps_point = enu_projector.reverse({0., 0., 0.});

  ASSERT_NEAR(gps_point.lat, origin_gps.lat, 0.00001);
  ASSERT_NEAR(gps_point.lon, origin_gps.lon, 0.00001);
  ASSERT_NEAR(gps_point.ele, origin_gps.ele, 0.00001);

  // Test point 111m east
  gps_point = enu_projector.reverse({111., 0., 0.});
  ASSERT_NEAR(gps_point.lat, east_gps.lat, 0.00001);
  ASSERT_NEAR(gps_point.lon, east_gps.lon, 0.00001);
  ASSERT_NEAR(gps_point.ele, east_gps.ele, 0.001);
}

int main(int argc, char** argv)
{
  testing::InitGoogleTest(&argc, argv);
  ros::init(argc, argv, "TestNode");
  return RUN_ALL_TESTS();
}
