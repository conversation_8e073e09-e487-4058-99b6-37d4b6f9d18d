<?xml version="1.0"?>
<launch>
  <arg name="velocity" default="40" />
  <arg name="output_file" default="/tmp/lane_waypoint.csv" />

  <node pkg="lane_planner" type="lane_navi" name="lane_navi" output="screen">
    <param name="velocity" value="$(arg velocity)" />
    <param name="output_file" value="$(arg output_file)" />
  </node>
  
  <node pkg="waypoint_maker" type="waypoint_marker_publisher" name="waypoint_marker_publisher"/>

  <arg name="use_ll2" default="false" />

  <node if="$(arg use_ll2)" pkg="lane_planner" type="lane_rule_lanelet2" name="lane_rule" output="screen" />
  <node unless="$(arg use_ll2)" pkg="lane_planner" type="lane_rule" name="lane_rule" output="screen" />

  <arg name="search_closest_waypoint_minimum_dt" default="5" doc="Minimum number of lookahead waypoints when searching closest_waypoint"/>
  <arg name="use_waypoint_speed" default="true" />
   <!-- 设置车辆速度 -->
  <arg name="waypoint_speed" default="5" doc="Speed for waypoints"/>

  <node pkg="lane_planner" type="lane_select" name="lane_select" output="screen">
    <param name="search_closest_waypoint_minimum_dt" value="$(arg search_closest_waypoint_minimum_dt)" />
    <param name="waypoint_speed" value="$(arg waypoint_speed)" />
    <param name="use_waypoint_speed" value="$(arg use_waypoint_speed)" />
  </node>



</launch>