#include "VehicleController.h"
#include "VelocityToPulse.h"
#include "djyxlogger.h"
#include <ros/ros.h>
#include <thread>

int main(int argc, char **argv)
{
  ros::init(argc, argv, "usbcan");
  ros::NodeHandle nh;

  djyx::Logger logger(200, 15); // 日志保存间隔（最大数量，最大天数）
  VehicleController vc(nh, logger);
  // PoseSaver poseSaver(nh);

  // 创建线程执行ProcessLoop
  std::thread processLoopThread(&VehicleController::ProcessLoop, &vc);

  // 创建另一个线程执行fakeCanMsgWheels
  std::thread fakeCanMsgWheelsThread(&VehicleController::fakeCanMsgWheels, &vc);

  // 使用ros::spin()来处理回调
  ros::spin();

  // 等待线程结束
  processLoopThread.join();
  fakeCanMsgWheelsThread.join();

  // 保存错误日志
  logger.saveErrorLog("error_log.txt");

  return 0;
}