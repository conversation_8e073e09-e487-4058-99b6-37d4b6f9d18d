#include <vector>
#include <usbcan/can_msg.h>
#include <ros/ros.h>
#include <usbcan_msgs/WheelsEncoder.h>
#include <usbcan_msgs/gps.h>
#include <usbcan_msgs/steering_angle.h>
#include <limits>
#include <usbcan/UsbCan.h>
#include <thread>
#include <chrono>
#include <boost/math/constants/constants.hpp>
#include <std_msgs/String.h>
#include <cmath>
#include <fstream>
#include <iostream>
#include <std_msgs/Float32.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/UInt8MultiArray.h>
#include <std_msgs/Int8MultiArray.h>
#include <std_msgs/UInt8.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Char.h>
#include <std_msgs/Int32.h>

class VelocityToPulse
{
private:
    double _pulse_counter{0};
    std::chrono::steady_clock::time_point _prev_time;
    float _wheel_radius;
    float _wheel_pulses;
    float _pulses_per_meter;

public:
    VelocityToPulse(float wheel_radius, unsigned int wheel_pulses) : _wheel_radius(wheel_radius),
                                                                     _wheel_pulses(static_cast<float>(wheel_pulses)),
                                                                     _prev_time(std::chrono::steady_clock::now())
    {
        _pulses_per_meter = _wheel_pulses / (2 * boost::math::constants::pi<float>() * _wheel_radius);
    }

    uint64_t operator()(float velocity)
    {
        auto t = std::chrono::steady_clock::now();
        std::chrono::duration<float> d = t - _prev_time;
        _prev_time = t;
        _pulse_counter += _pulses_per_meter * velocity * d.count();
        if (_pulse_counter > 1e14)
        {
            _pulse_counter = 0;
        }
        return static_cast<uint64_t>(std::round(_pulse_counter));
    }
    void reset()
    {
        _pulse_counter = 0;
        _prev_time = std::chrono::steady_clock::now();
    }
};